@todoapi_HostAddress = http://localhost:5000

# Welcome page
GET {{todoapi_HostAddress}}/
Accept: text/html

###

# Swagger UI - Open this in a browser
GET {{todoapi_HostAddress}}/swagger/
Accept: application/json

###

# Swagger JSON - API documentation
GET {{todoapi_HostAddress}}/swagger/v1/swagger.json
Accept: application/json

###

# Weather Forecast endpoint (legacy)
GET {{todoapi_HostAddress}}/weatherforecast/
Accept: application/json

###

# === TODO API ENDPOINTS ===

# Get all tasks
GET {{todoapi_HostAddress}}/api/tasks
Accept: application/json

###

# Create a new task
POST {{todoapi_HostAddress}}/api/tasks
Content-Type: application/json

{
  "title": "Test task from HTTP file",
  "notes": "This is a test task created via HTTP request",
  "isImportant": false,
  "dueDate": "2024-01-15",
  "assigneeIds": ["1"]
}

###

# Get important tasks
GET {{todoapi_HostAddress}}/api/tasks/important
Accept: application/json

###

# Get planned tasks (with due dates)
GET {{todoapi_HostAddress}}/api/tasks/planned
Accept: application/json

###

# Get my day tasks (today's date)
GET {{todoapi_HostAddress}}/api/tasks/my-day/{{$datetime "yyyy-MM-dd"}}
Accept: application/json

###

# Update a task (replace {id} with actual task ID)
PUT {{todoapi_HostAddress}}/api/tasks/1
Content-Type: application/json

{
  "title": "Updated task title",
  "isCompleted": true,
  "isImportant": true
}

###

# Bulk operation - complete multiple tasks
POST {{todoapi_HostAddress}}/api/tasks/bulk
Content-Type: application/json

{
  "taskIds": ["1", "2"],
  "operation": "complete"
}

###

# Delete a task (replace {id} with actual task ID)
DELETE {{todoapi_HostAddress}}/api/tasks/1

###

# Get all lists
GET {{todoapi_HostAddress}}/api/lists
Accept: application/json

###

# Create a new list
POST {{todoapi_HostAddress}}/api/lists
Content-Type: application/json

{
  "name": "Test List",
  "icon": "??"
}

###

# Get all users
GET {{todoapi_HostAddress}}/api/users
Accept: application/json

###

# Create a new user
POST {{todoapi_HostAddress}}/api/users
Content-Type: application/json

{
  "name": "Test User",
  "email": "<EMAIL>",
  "avatar": "#FF5722"
}

###

# Get application statistics
GET {{todoapi_HostAddress}}/api/stats
Accept: application/json

###

# Get tasks by specific list (replace {listId} with actual list ID)
GET {{todoapi_HostAddress}}/api/tasks/by-list/grocery
Accept: application/json

###

# Get tasks by specific user (replace {userId} with actual user ID)
GET {{todoapi_HostAddress}}/api/tasks/by-user/1
Accept: application/json

###
