<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear and Seed Database</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            min-width: 200px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button.danger {
            background-color: #dc3545;
        }
        .button.danger:hover {
            background-color: #c82333;
        }
        .button.success {
            background-color: #28a745;
        }
        .button.success:hover {
            background-color: #218838;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .button-group {
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗃️ Database Management</h1>
        
        <div class="button-group">
            <button class="button danger" onclick="clearDatabase()">🗑️ Clear All Data</button>
            <button class="button success" onclick="seedDatabase()">🌱 Add Sample Data</button>
            <button class="button" onclick="clearAndSeed()">🔄 Clear & Seed</button>
        </div>

        <div id="status"></div>
        <div id="log" class="log"></div>
    </div>

    <script>
        const API_BASE = '/api';
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function apiRequest(endpoint, options = {}) {
            const url = `${API_BASE}${endpoint}`;
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            
            if (data.success === false) {
                throw new Error(data.message || 'API request failed');
            }

            return data.data || data;
        }

        async function clearDatabase() {
            try {
                showStatus('🗑️ Clearing database...', 'info');
                log('Starting database clear operation...');

                // Get all data first
                const [tasks, lists, users] = await Promise.all([
                    apiRequest('/tasks'),
                    apiRequest('/lists'),
                    apiRequest('/users')
                ]);

                log(`Found: ${tasks.length} tasks, ${lists.length} lists, ${users.length} users`);

                // Delete all tasks
                for (const task of tasks) {
                    await apiRequest(`/tasks/${task.id}`, { method: 'DELETE' });
                    log(`Deleted task: ${task.title}`);
                }

                // Delete all lists
                for (const list of lists) {
                    await apiRequest(`/lists/${list.id}`, { method: 'DELETE' });
                    log(`Deleted list: ${list.name}`);
                }

                // Delete all users
                for (const user of users) {
                    await apiRequest(`/users/${user.id}`, { method: 'DELETE' });
                    log(`Deleted user: ${user.name}`);
                }

                showStatus('✅ Database cleared successfully!', 'success');
                log('Database clear completed successfully!');

            } catch (error) {
                showStatus(`❌ Error clearing database: ${error.message}`, 'error');
                log(`Error: ${error.message}`);
            }
        }

        async function seedDatabase() {
            try {
                showStatus('🌱 Adding sample data...', 'info');
                log('Starting database seed operation...');

                // Sample data
                const seedData = {
                    users: [
                        { name: "Dad", avatar: "#4CAF50", email: "<EMAIL>" },
                        { name: "Mom", avatar: "#2196F3", email: "<EMAIL>" },
                        { name: "Jr. John", avatar: "#FF9800", email: "<EMAIL>" },
                        { name: "Mike", avatar: "#9C27B0", email: "<EMAIL>" },
                        { name: "Louie", avatar: "#E91E63", email: "<EMAIL>" }
                    ],
                    lists: [
                        { name: "Home", icon: "🏠" },
                        { name: "payment", icon: "💳" },
                        { name: "DISCIPLINE", icon: "📋" }
                    ]
                };

                // Create users first
                const createdUsers = [];
                for (const user of seedData.users) {
                    const newUser = await apiRequest('/users', {
                        method: 'POST',
                        body: JSON.stringify(user)
                    });
                    createdUsers.push(newUser);
                    log(`Created user: ${user.name}`);
                }

                // Create lists
                const createdLists = [];
                for (const list of seedData.lists) {
                    const newList = await apiRequest('/lists', {
                        method: 'POST',
                        body: JSON.stringify(list)
                    });
                    createdLists.push(newList);
                    log(`Created list: ${list.name}`);
                }

                // Create sample tasks
                const sampleTasks = [
                    {
                        title: "Haircut",
                        listId: createdLists[0].id, // Home
                        assigneeIds: [createdUsers[2].id, createdUsers[3].id] // Jr. John, Mike
                    },
                    {
                        title: "Find Job",
                        listId: createdLists[0].id, // Home
                        assigneeIds: [createdUsers[0].id, createdUsers[1].id, createdUsers[2].id, createdUsers[4].id] // Dad, Mom, Jr. John, Louie
                    },
                    {
                        title: "Clean the house",
                        listId: createdLists[0].id, // Home
                        assigneeIds: [createdUsers[0].id, createdUsers[1].id, createdUsers[2].id, createdUsers[3].id] // All family
                    },
                    {
                        title: "Catch the Rat",
                        listId: createdLists[0].id, // Home
                        assigneeIds: [createdUsers[4].id] // Louie
                    },
                    {
                        title: "HOA 99",
                        dueDate: "2025-10-01",
                        listId: createdLists[1].id // payment
                    },
                    {
                        title: "HOUSE TAX",
                        dueDate: "2025-09-30",
                        listId: createdLists[1].id // payment
                    },
                    {
                        title: "CHASE 1 CHASE 2",
                        dueDate: "2025-10-13",
                        listId: createdLists[1].id // payment
                    },
                    {
                        title: "Apple Card",
                        listId: createdLists[1].id // payment
                    },
                    {
                        title: "GET UP 6 AM",
                        listId: createdLists[2].id // DISCIPLINE
                    },
                    {
                        title: "INTERNET CLOSE 8 PM",
                        listId: createdLists[2].id // DISCIPLINE
                    },
                    {
                        title: "Pay Apple card",
                        listId: createdLists[0].id, // Home
                        isImportant: true
                    }
                ];

                for (const task of sampleTasks) {
                    const newTask = await apiRequest('/tasks', {
                        method: 'POST',
                        body: JSON.stringify(task)
                    });
                    log(`Created task: ${task.title}`);
                }

                showStatus('✅ Sample data added successfully!', 'success');
                log(`Seed completed! Created ${createdUsers.length} users, ${createdLists.length} lists, ${sampleTasks.length} tasks`);

            } catch (error) {
                showStatus(`❌ Error seeding database: ${error.message}`, 'error');
                log(`Error: ${error.message}`);
            }
        }

        async function clearAndSeed() {
            try {
                showStatus('🔄 Clearing and seeding database...', 'info');
                log('Starting clear and seed operation...');
                
                await clearDatabase();
                await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
                await seedDatabase();
                
                showStatus('✅ Clear and seed completed successfully!', 'success');
                log('Clear and seed operation completed!');
                
            } catch (error) {
                showStatus(`❌ Error during clear and seed: ${error.message}`, 'error');
                log(`Error: ${error.message}`);
            }
        }

        // Initialize
        log('Database management tool ready');
        showStatus('Ready to manage database', 'info');
    </script>
</body>
</html>
