using Microsoft.AspNetCore.Mvc;
using todoapi.DTOs;
using todoapi.Services;

namespace todoapi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class UsersController : ControllerBase
{
    private readonly ITodoService _todoService;
    private readonly ILogger<UsersController> _logger;

    public UsersController(ITodoService todoService, ILogger<UsersController> logger)
    {
        _todoService = todoService;
        _logger = logger;
    }

    /// <summary>
    /// Get all users
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<IEnumerable<UserDto>>>> GetAllUsers()
    {
        try
        {
            var users = await _todoService.GetAllUsersAsync();
            return Ok(new ApiResponse<IEnumerable<UserDto>>
            {
                Success = true,
                Data = users,
                Message = "Users retrieved successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all users");
            return StatusCode(500, new ApiResponse<IEnumerable<UserDto>>
            {
                Success = false,
                Message = "Internal server error",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Get user by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<ApiResponse<UserDto>>> GetUser(string id)
    {
        try
        {
            var user = await _todoService.GetUserByIdAsync(id);
            if (user == null)
            {
                return NotFound(new ApiResponse<UserDto>
                {
                    Success = false,
                    Message = "User not found"
                });
            }

            return Ok(new ApiResponse<UserDto>
            {
                Success = true,
                Data = user,
                Message = "User retrieved successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user {UserId}", id);
            return StatusCode(500, new ApiResponse<UserDto>
            {
                Success = false,
                Message = "Internal server error",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Create a new user
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<ApiResponse<UserDto>>> CreateUser([FromBody] CreateUserDto userDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new ApiResponse<UserDto>
                {
                    Success = false,
                    Message = "Validation failed",
                    Errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList()
                });
            }

            var createdUser = await _todoService.CreateUserAsync(userDto);
            return CreatedAtAction(nameof(GetUser), new { id = createdUser.Id }, 
                new ApiResponse<UserDto>
                {
                    Success = true,
                    Data = createdUser,
                    Message = "User created successfully"
                });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating user");
            return StatusCode(500, new ApiResponse<UserDto>
            {
                Success = false,
                Message = "Internal server error",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Update an existing user
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<ApiResponse<UserDto>>> UpdateUser(string id, [FromBody] CreateUserDto userDto)
    {
        try
        {
            var updatedUser = await _todoService.UpdateUserAsync(id, userDto);
            if (updatedUser == null)
            {
                return NotFound(new ApiResponse<UserDto>
                {
                    Success = false,
                    Message = "User not found"
                });
            }

            return Ok(new ApiResponse<UserDto>
            {
                Success = true,
                Data = updatedUser,
                Message = "User updated successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user {UserId}", id);
            return StatusCode(500, new ApiResponse<UserDto>
            {
                Success = false,
                Message = "Internal server error",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Delete a user
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult<ApiResponse<bool>>> DeleteUser(string id)
    {
        try
        {
            var result = await _todoService.DeleteUserAsync(id);
            if (!result)
            {
                return NotFound(new ApiResponse<bool>
                {
                    Success = false,
                    Message = "User not found"
                });
            }

            return Ok(new ApiResponse<bool>
            {
                Success = true,
                Data = true,
                Message = "User deleted successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user {UserId}", id);
            return StatusCode(500, new ApiResponse<bool>
            {
                Success = false,
                Message = "Internal server error",
                Errors = { ex.Message }
            });
        }
    }
}

[ApiController]
[Route("api/[controller]")]
public class StatsController : ControllerBase
{
    private readonly ITodoService _todoService;
    private readonly ILogger<StatsController> _logger;

    public StatsController(ITodoService todoService, ILogger<StatsController> logger)
    {
        _todoService = todoService;
        _logger = logger;
    }

    /// <summary>
    /// Get application statistics
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<object>>> GetStats()
    {
        try
        {
            var stats = await _todoService.GetStatsAsync();
            return Ok(new ApiResponse<object>
            {
                Success = true,
                Data = stats,
                Message = "Statistics retrieved successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting statistics");
            return StatusCode(500, new ApiResponse<object>
            {
                Success = false,
                Message = "Internal server error",
                Errors = { ex.Message }
            });
        }
    }
}