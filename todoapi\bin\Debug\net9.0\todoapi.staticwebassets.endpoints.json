{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "app.js", "AssetFile": "app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000279173646"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3581"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"k9kaQHrddU4x76jwtJ+EzHZ9U8cPYjGWB52wnBq4XRA=\""}, {"Name": "ETag", "Value": "W/\"H4gxE1xMACmg7wuf/flp2mMTGKwfv5mwKRRaTBoCjRY=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H4gxE1xMACmg7wuf/flp2mMTGKwfv5mwKRRaTBoCjRY="}]}, {"Route": "app.js", "AssetFile": "app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16435"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"H4gxE1xMACmg7wuf/flp2mMTGKwfv5mwKRRaTBoCjRY=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:46:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H4gxE1xMACmg7wuf/flp2mMTGKwfv5mwKRRaTBoCjRY="}]}, {"Route": "app.js.gz", "AssetFile": "app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3581"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"k9kaQHrddU4x76jwtJ+EzHZ9U8cPYjGWB52wnBq4XRA=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k9kaQHrddU4x76jwtJ+EzHZ9U8cPYjGWB52wnBq4XRA="}]}, {"Route": "app.lntny8cd3c.js", "AssetFile": "app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000279173646"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3581"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"k9kaQHrddU4x76jwtJ+EzHZ9U8cPYjGWB52wnBq4XRA=\""}, {"Name": "ETag", "Value": "W/\"H4gxE1xMACmg7wuf/flp2mMTGKwfv5mwKRRaTBoCjRY=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lntny8cd3c"}, {"Name": "integrity", "Value": "sha256-H4gxE1xMACmg7wuf/flp2mMTGKwfv5mwKRRaTBoCjRY="}, {"Name": "label", "Value": "app.js"}]}, {"Route": "app.lntny8cd3c.js", "AssetFile": "app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "16435"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"H4gxE1xMACmg7wuf/flp2mMTGKwfv5mwKRRaTBoCjRY=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:46:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lntny8cd3c"}, {"Name": "integrity", "Value": "sha256-H4gxE1xMACmg7wuf/flp2mMTGKwfv5mwKRRaTBoCjRY="}, {"Name": "label", "Value": "app.js"}]}, {"Route": "app.lntny8cd3c.js.gz", "AssetFile": "app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3581"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"k9kaQHrddU4x76jwtJ+EzHZ9U8cPYjGWB52wnBq4XRA=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lntny8cd3c"}, {"Name": "integrity", "Value": "sha256-k9kaQHrddU4x76jwtJ+EzHZ9U8cPYjGWB52wnBq4XRA="}, {"Name": "label", "Value": "app.js.gz"}]}, {"Route": "backup/index.5lbvunm6pn.html", "AssetFile": "backup/index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001314060447"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "760"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"OFeln9w3U2lkCl5GtvBgJtU2fh1GaGB0HrJkH+f0wrs=\""}, {"Name": "ETag", "Value": "W/\"lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5lbvunm6pn"}, {"Name": "integrity", "Value": "sha256-lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ="}, {"Name": "label", "Value": "backup/index.html"}]}, {"Route": "backup/index.5lbvunm6pn.html", "AssetFile": "backup/index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1941"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 03:04:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5lbvunm6pn"}, {"Name": "integrity", "Value": "sha256-lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ="}, {"Name": "label", "Value": "backup/index.html"}]}, {"Route": "backup/index.5lbvunm6pn.html.gz", "AssetFile": "backup/index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "760"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"OFeln9w3U2lkCl5GtvBgJtU2fh1GaGB0HrJkH+f0wrs=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5lbvunm6pn"}, {"Name": "integrity", "Value": "sha256-OFeln9w3U2lkCl5GtvBgJtU2fh1GaGB0HrJkH+f0wrs="}, {"Name": "label", "Value": "backup/index.html.gz"}]}, {"Route": "backup/index.html", "AssetFile": "backup/index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001314060447"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "760"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"OFeln9w3U2lkCl5GtvBgJtU2fh1GaGB0HrJkH+f0wrs=\""}, {"Name": "ETag", "Value": "W/\"lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ="}]}, {"Route": "backup/index.html", "AssetFile": "backup/index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1941"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 03:04:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ="}]}, {"Route": "backup/index.html.gz", "AssetFile": "backup/index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "760"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"OFeln9w3U2lkCl5GtvBgJtU2fh1GaGB0HrJkH+f0wrs=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OFeln9w3U2lkCl5GtvBgJtU2fh1GaGB0HrJkH+f0wrs="}]}, {"Route": "backup/index9 - Copy.html", "AssetFile": "backup/index9 - Copy.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000047054395"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "21251"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"gQTTL93AbFv+lBWIRxh5OVS81Yz4rVAtF0z9xUM/SMQ=\""}, {"Name": "ETag", "Value": "W/\"vq9BTSuUyPJUcsH6XlQUs9F9kV3CaujKXxugiI9tTho=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vq9BTSuUyPJUcsH6XlQUs9F9kV3CaujKXxugiI9tTho="}]}, {"Route": "backup/index9 - Copy.html", "AssetFile": "backup/index9 - Copy.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "133787"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"vq9BTSuUyPJUcsH6XlQUs9F9kV3CaujKXxugiI9tTho=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vq9BTSuUyPJUcsH6XlQUs9F9kV3CaujKXxugiI9tTho="}]}, {"Route": "backup/index9 - Copy.html.gz", "AssetFile": "backup/index9 - Copy.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "21251"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"gQTTL93AbFv+lBWIRxh5OVS81Yz4rVAtF0z9xUM/SMQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gQTTL93AbFv+lBWIRxh5OVS81Yz4rVAtF0z9xUM/SMQ="}]}, {"Route": "backup/index9 - Copy.uk5yjv168n.html", "AssetFile": "backup/index9 - Copy.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000047054395"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "21251"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"gQTTL93AbFv+lBWIRxh5OVS81Yz4rVAtF0z9xUM/SMQ=\""}, {"Name": "ETag", "Value": "W/\"vq9BTSuUyPJUcsH6XlQUs9F9kV3CaujKXxugiI9tTho=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uk5yjv168n"}, {"Name": "integrity", "Value": "sha256-vq9BTSuUyPJUcsH6XlQUs9F9kV3CaujKXxugiI9tTho="}, {"Name": "label", "Value": "backup/index9 - Copy.html"}]}, {"Route": "backup/index9 - Copy.uk5yjv168n.html", "AssetFile": "backup/index9 - Copy.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "133787"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"vq9BTSuUyPJUcsH6XlQUs9F9kV3CaujKXxugiI9tTho=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uk5yjv168n"}, {"Name": "integrity", "Value": "sha256-vq9BTSuUyPJUcsH6XlQUs9F9kV3CaujKXxugiI9tTho="}, {"Name": "label", "Value": "backup/index9 - Copy.html"}]}, {"Route": "backup/index9 - Copy.uk5yjv168n.html.gz", "AssetFile": "backup/index9 - Copy.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "21251"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"gQTTL93AbFv+lBWIRxh5OVS81Yz4rVAtF0z9xUM/SMQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uk5yjv168n"}, {"Name": "integrity", "Value": "sha256-gQTTL93AbFv+lBWIRxh5OVS81Yz4rVAtF0z9xUM/SMQ="}, {"Name": "label", "Value": "backup/index9 - Copy.html.gz"}]}, {"Route": "backup/index9-backup.595uqhdyu2.html", "AssetFile": "backup/index9-backup.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000058830451"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16997"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"J0wQBfJ/jM46tUFbkcLrW26DGvVvT9zjpd6hWDQulag=\""}, {"Name": "ETag", "Value": "W/\"GQRjVfF7pbEugsQ7SDV9U/FuUPuY5ZOD9tVJiNerGB8=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "595uqhdyu2"}, {"Name": "integrity", "Value": "sha256-GQRjVfF7pbEugsQ7SDV9U/FuUPuY5ZOD9tVJiNerGB8="}, {"Name": "label", "Value": "backup/index9-backup.html"}]}, {"Route": "backup/index9-backup.595uqhdyu2.html", "AssetFile": "backup/index9-backup.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "100054"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"GQRjVfF7pbEugsQ7SDV9U/FuUPuY5ZOD9tVJiNerGB8=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:24:04 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "595uqhdyu2"}, {"Name": "integrity", "Value": "sha256-GQRjVfF7pbEugsQ7SDV9U/FuUPuY5ZOD9tVJiNerGB8="}, {"Name": "label", "Value": "backup/index9-backup.html"}]}, {"Route": "backup/index9-backup.595uqhdyu2.html.gz", "AssetFile": "backup/index9-backup.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16997"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"J0wQBfJ/jM46tUFbkcLrW26DGvVvT9zjpd6hWDQulag=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "595uqhdyu2"}, {"Name": "integrity", "Value": "sha256-J0wQBfJ/jM46tUFbkcLrW26DGvVvT9zjpd6hWDQulag="}, {"Name": "label", "Value": "backup/index9-backup.html.gz"}]}, {"Route": "backup/index9-backup.html", "AssetFile": "backup/index9-backup.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000058830451"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16997"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"J0wQBfJ/jM46tUFbkcLrW26DGvVvT9zjpd6hWDQulag=\""}, {"Name": "ETag", "Value": "W/\"GQRjVfF7pbEugsQ7SDV9U/FuUPuY5ZOD9tVJiNerGB8=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GQRjVfF7pbEugsQ7SDV9U/FuUPuY5ZOD9tVJiNerGB8="}]}, {"Route": "backup/index9-backup.html", "AssetFile": "backup/index9-backup.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "100054"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"GQRjVfF7pbEugsQ7SDV9U/FuUPuY5ZOD9tVJiNerGB8=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:24:04 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GQRjVfF7pbEugsQ7SDV9U/FuUPuY5ZOD9tVJiNerGB8="}]}, {"Route": "backup/index9-backup.html.gz", "AssetFile": "backup/index9-backup.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16997"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"J0wQBfJ/jM46tUFbkcLrW26DGvVvT9zjpd6hWDQulag=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J0wQBfJ/jM46tUFbkcLrW26DGvVvT9zjpd6hWDQulag="}]}, {"Route": "backup/index9-clean.html", "AssetFile": "backup/index9-clean.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000301750151"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3313"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"rmcl4NLapeBROJihhf6i27bvTpoeV8nl1TsyesALXv8=\""}, {"Name": "ETag", "Value": "W/\"qsNdnqEp0inrPmO8N/ZnGqNgSDA61ZyrmjWpC/at1QU=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qsNdnqEp0inrPmO8N/ZnGqNgSDA61ZyrmjWpC/at1QU="}]}, {"Route": "backup/index9-clean.html", "AssetFile": "backup/index9-clean.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19835"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"qsNdnqEp0inrPmO8N/ZnGqNgSDA61ZyrmjWpC/at1QU=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:43:06 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qsNdnqEp0inrPmO8N/ZnGqNgSDA61ZyrmjWpC/at1QU="}]}, {"Route": "backup/index9-clean.html.gz", "AssetFile": "backup/index9-clean.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3313"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"rmcl4NLapeBROJihhf6i27bvTpoeV8nl1TsyesALXv8=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rmcl4NLapeBROJihhf6i27bvTpoeV8nl1TsyesALXv8="}]}, {"Route": "backup/index9-clean.q7oz31h1by.html", "AssetFile": "backup/index9-clean.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000301750151"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3313"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"rmcl4NLapeBROJihhf6i27bvTpoeV8nl1TsyesALXv8=\""}, {"Name": "ETag", "Value": "W/\"qsNdnqEp0inrPmO8N/ZnGqNgSDA61ZyrmjWpC/at1QU=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q7oz31h1by"}, {"Name": "integrity", "Value": "sha256-qsNdnqEp0inrPmO8N/ZnGqNgSDA61ZyrmjWpC/at1QU="}, {"Name": "label", "Value": "backup/index9-clean.html"}]}, {"Route": "backup/index9-clean.q7oz31h1by.html", "AssetFile": "backup/index9-clean.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19835"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"qsNdnqEp0inrPmO8N/ZnGqNgSDA61ZyrmjWpC/at1QU=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:43:06 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q7oz31h1by"}, {"Name": "integrity", "Value": "sha256-qsNdnqEp0inrPmO8N/ZnGqNgSDA61ZyrmjWpC/at1QU="}, {"Name": "label", "Value": "backup/index9-clean.html"}]}, {"Route": "backup/index9-clean.q7oz31h1by.html.gz", "AssetFile": "backup/index9-clean.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3313"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"rmcl4NLapeBROJihhf6i27bvTpoeV8nl1TsyesALXv8=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q7oz31h1by"}, {"Name": "integrity", "Value": "sha256-rmcl4NLapeBROJihhf6i27bvTpoeV8nl1TsyesALXv8="}, {"Name": "label", "Value": "backup/index9-clean.html.gz"}]}, {"Route": "backup/index9.html", "AssetFile": "backup/index9.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000303582271"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3293"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"/iCnh7XGsG6RmEuHAqxqla/uuaAIGz3GvoyD/4MDW08=\""}, {"Name": "ETag", "Value": "W/\"VA5sqO/RUbif/XskndwzuQNSH2ux7gWVWisbWAmTSjU=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VA5sqO/RUbif/XskndwzuQNSH2ux7gWVWisbWAmTSjU="}]}, {"Route": "backup/index9.html", "AssetFile": "backup/index9.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19419"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"VA5sqO/RUbif/XskndwzuQNSH2ux7gWVWisbWAmTSjU=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VA5sqO/RUbif/XskndwzuQNSH2ux7gWVWisbWAmTSjU="}]}, {"Route": "backup/index9.html.gz", "AssetFile": "backup/index9.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3293"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"/iCnh7XGsG6RmEuHAqxqla/uuaAIGz3GvoyD/4MDW08=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/iCnh7XGsG6RmEuHAqxqla/uuaAIGz3GvoyD/4MDW08="}]}, {"Route": "backup/index9.olqgrn1tj4.html", "AssetFile": "backup/index9.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000303582271"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3293"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"/iCnh7XGsG6RmEuHAqxqla/uuaAIGz3GvoyD/4MDW08=\""}, {"Name": "ETag", "Value": "W/\"VA5sqO/RUbif/XskndwzuQNSH2ux7gWVWisbWAmTSjU=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "olqgrn1tj4"}, {"Name": "integrity", "Value": "sha256-VA5sqO/RUbif/XskndwzuQNSH2ux7gWVWisbWAmTSjU="}, {"Name": "label", "Value": "backup/index9.html"}]}, {"Route": "backup/index9.olqgrn1tj4.html", "AssetFile": "backup/index9.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19419"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"VA5sqO/RUbif/XskndwzuQNSH2ux7gWVWisbWAmTSjU=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "olqgrn1tj4"}, {"Name": "integrity", "Value": "sha256-VA5sqO/RUbif/XskndwzuQNSH2ux7gWVWisbWAmTSjU="}, {"Name": "label", "Value": "backup/index9.html"}]}, {"Route": "backup/index9.olqgrn1tj4.html.gz", "AssetFile": "backup/index9.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3293"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"/iCnh7XGsG6RmEuHAqxqla/uuaAIGz3GvoyD/4MDW08=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "olqgrn1tj4"}, {"Name": "integrity", "Value": "sha256-/iCnh7XGsG6RmEuHAqxqla/uuaAIGz3GvoyD/4MDW08="}, {"Name": "label", "Value": "backup/index9.html.gz"}]}, {"Route": "index10.5lfc2i8sl7.html", "AssetFile": "index10.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000366300366"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2729"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"W6zLe8LlKGYZrXGrS7Ssd8WRqgDFNG1lJX6ENjhgdbQ=\""}, {"Name": "ETag", "Value": "W/\"F9zrKUdWGGfzx3oZExo1GckqOSWq8RRD0//i3xJ7vP0=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5lfc2i8sl7"}, {"Name": "integrity", "Value": "sha256-F9zrKUdWGGfzx3oZExo1GckqOSWq8RRD0//i3xJ7vP0="}, {"Name": "label", "Value": "index10.html"}]}, {"Route": "index10.5lfc2i8sl7.html", "AssetFile": "index10.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "16291"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"F9zrKUdWGGfzx3oZExo1GckqOSWq8RRD0//i3xJ7vP0=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:42:30 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5lfc2i8sl7"}, {"Name": "integrity", "Value": "sha256-F9zrKUdWGGfzx3oZExo1GckqOSWq8RRD0//i3xJ7vP0="}, {"Name": "label", "Value": "index10.html"}]}, {"Route": "index10.5lfc2i8sl7.html.gz", "AssetFile": "index10.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2729"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"W6zLe8LlKGYZrXGrS7Ssd8WRqgDFNG1lJX6ENjhgdbQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5lfc2i8sl7"}, {"Name": "integrity", "Value": "sha256-W6zLe8LlKGYZrXGrS7Ssd8WRqgDFNG1lJX6ENjhgdbQ="}, {"Name": "label", "Value": "index10.html.gz"}]}, {"Route": "index10.html", "AssetFile": "index10.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000366300366"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2729"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"W6zLe8LlKGYZrXGrS7Ssd8WRqgDFNG1lJX6ENjhgdbQ=\""}, {"Name": "ETag", "Value": "W/\"F9zrKUdWGGfzx3oZExo1GckqOSWq8RRD0//i3xJ7vP0=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-F9zrKUdWGGfzx3oZExo1GckqOSWq8RRD0//i3xJ7vP0="}]}, {"Route": "index10.html", "AssetFile": "index10.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16291"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"F9zrKUdWGGfzx3oZExo1GckqOSWq8RRD0//i3xJ7vP0=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:42:30 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-F9zrKUdWGGfzx3oZExo1GckqOSWq8RRD0//i3xJ7vP0="}]}, {"Route": "index10.html.gz", "AssetFile": "index10.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2729"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"W6zLe8LlKGYZrXGrS7Ssd8WRqgDFNG1lJX6ENjhgdbQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-W6zLe8LlKGYZrXGrS7Ssd8WRqgDFNG1lJX6ENjhgdbQ="}]}, {"Route": "simple-app.3gr8uxwobs.js", "AssetFile": "simple-app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000397614314"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2514"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"egRMIxG9F7VG+0SYVhcDe+cylBYKlM6GPigJvKJcrno=\""}, {"Name": "ETag", "Value": "W/\"8z5pejHTQgQmtBcbvj2w90/mO0VutwH7czoAylkH1Qw=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3gr8uxwobs"}, {"Name": "integrity", "Value": "sha256-8z5pejHTQgQmtBcbvj2w90/mO0VutwH7czoAylkH1Qw="}, {"Name": "label", "Value": "simple-app.js"}]}, {"Route": "simple-app.3gr8uxwobs.js", "AssetFile": "simple-app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10188"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8z5pejHTQgQmtBcbvj2w90/mO0VutwH7czoAylkH1Qw=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:40:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3gr8uxwobs"}, {"Name": "integrity", "Value": "sha256-8z5pejHTQgQmtBcbvj2w90/mO0VutwH7czoAylkH1Qw="}, {"Name": "label", "Value": "simple-app.js"}]}, {"Route": "simple-app.3gr8uxwobs.js.gz", "AssetFile": "simple-app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2514"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"egRMIxG9F7VG+0SYVhcDe+cylBYKlM6GPigJvKJcrno=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3gr8uxwobs"}, {"Name": "integrity", "Value": "sha256-egRMIxG9F7VG+0SYVhcDe+cylBYKlM6GPigJvKJcrno="}, {"Name": "label", "Value": "simple-app.js.gz"}]}, {"Route": "simple-app.js", "AssetFile": "simple-app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000397614314"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2514"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"egRMIxG9F7VG+0SYVhcDe+cylBYKlM6GPigJvKJcrno=\""}, {"Name": "ETag", "Value": "W/\"8z5pejHTQgQmtBcbvj2w90/mO0VutwH7czoAylkH1Qw=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8z5pejHTQgQmtBcbvj2w90/mO0VutwH7czoAylkH1Qw="}]}, {"Route": "simple-app.js", "AssetFile": "simple-app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10188"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8z5pejHTQgQmtBcbvj2w90/mO0VutwH7czoAylkH1Qw=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:40:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8z5pejHTQgQmtBcbvj2w90/mO0VutwH7czoAylkH1Qw="}]}, {"Route": "simple-app.js.gz", "AssetFile": "simple-app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2514"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"egRMIxG9F7VG+0SYVhcDe+cylBYKlM6GPigJvKJcrno=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-egRMIxG9F7VG+0SYVhcDe+cylBYKlM6GPigJvKJcrno="}]}, {"Route": "simple-ui.js", "AssetFile": "simple-ui.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000417014178"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2397"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cp1ICAQjIuI6Kn9i06hEofwik2J7EZj/BM4daQRLJmg=\""}, {"Name": "ETag", "Value": "W/\"7fftasEFBE4Gwzu8+Uj+VMxuJwGuGTVTQnRiFePPBLg=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7fftasEFBE4Gwzu8+Uj+VMxuJwGuGTVTQnRiFePPBLg="}]}, {"Route": "simple-ui.js", "AssetFile": "simple-ui.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8974"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"7fftasEFBE4Gwzu8+Uj+VMxuJwGuGTVTQnRiFePPBLg=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:41:06 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7fftasEFBE4Gwzu8+Uj+VMxuJwGuGTVTQnRiFePPBLg="}]}, {"Route": "simple-ui.js.gz", "AssetFile": "simple-ui.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2397"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cp1ICAQjIuI6Kn9i06hEofwik2J7EZj/BM4daQRLJmg=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cp1ICAQjIuI6Kn9i06hEofwik2J7EZj/BM4daQRLJmg="}]}, {"Route": "simple-ui.xjex4zorxw.js", "AssetFile": "simple-ui.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000417014178"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2397"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cp1ICAQjIuI6Kn9i06hEofwik2J7EZj/BM4daQRLJmg=\""}, {"Name": "ETag", "Value": "W/\"7fftasEFBE4Gwzu8+Uj+VMxuJwGuGTVTQnRiFePPBLg=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xjex4zorxw"}, {"Name": "integrity", "Value": "sha256-7fftasEFBE4Gwzu8+Uj+VMxuJwGuGTVTQnRiFePPBLg="}, {"Name": "label", "Value": "simple-ui.js"}]}, {"Route": "simple-ui.xjex4zorxw.js", "AssetFile": "simple-ui.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8974"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"7fftasEFBE4Gwzu8+Uj+VMxuJwGuGTVTQnRiFePPBLg=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:41:06 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xjex4zorxw"}, {"Name": "integrity", "Value": "sha256-7fftasEFBE4Gwzu8+Uj+VMxuJwGuGTVTQnRiFePPBLg="}, {"Name": "label", "Value": "simple-ui.js"}]}, {"Route": "simple-ui.xjex4zorxw.js.gz", "AssetFile": "simple-ui.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2397"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cp1ICAQjIuI6Kn9i06hEofwik2J7EZj/BM4daQRLJmg=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 23:45:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xjex4zorxw"}, {"Name": "integrity", "Value": "sha256-cp1ICAQjIuI6Kn9i06hEofwik2J7EZj/BM4daQRLJmg="}, {"Name": "label", "Value": "simple-ui.js.gz"}]}, {"Route": "styles.css", "AssetFile": "styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000229305205"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4360"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5SsbBNT/tJF3BhUIoOeC6Hpd9FQozc56iUv2Qap7q9I=\""}, {"Name": "ETag", "Value": "W/\"jrlspTk8eSbhsrt52DJGB/Nz9GniPonpBzfSfcVbagM=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jrlspTk8eSbhsrt52DJGB/Nz9GniPonpBzfSfcVbagM="}]}, {"Route": "styles.css", "AssetFile": "styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "26007"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"jrlspTk8eSbhsrt52DJGB/Nz9GniPonpBzfSfcVbagM=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:43:06 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jrlspTk8eSbhsrt52DJGB/Nz9GniPonpBzfSfcVbagM="}]}, {"Route": "styles.css.gz", "AssetFile": "styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4360"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5SsbBNT/tJF3BhUIoOeC6Hpd9FQozc56iUv2Qap7q9I=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5SsbBNT/tJF3BhUIoOeC6Hpd9FQozc56iUv2Qap7q9I="}]}, {"Route": "styles.mb95wunvkt.css", "AssetFile": "styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000229305205"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4360"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5SsbBNT/tJF3BhUIoOeC6Hpd9FQozc56iUv2Qap7q9I=\""}, {"Name": "ETag", "Value": "W/\"jrlspTk8eSbhsrt52DJGB/Nz9GniPonpBzfSfcVbagM=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mb95wunvkt"}, {"Name": "integrity", "Value": "sha256-jrlspTk8eSbhsrt52DJGB/Nz9GniPonpBzfSfcVbagM="}, {"Name": "label", "Value": "styles.css"}]}, {"Route": "styles.mb95wunvkt.css", "AssetFile": "styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "26007"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"jrlspTk8eSbhsrt52DJGB/Nz9GniPonpBzfSfcVbagM=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:43:06 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mb95wunvkt"}, {"Name": "integrity", "Value": "sha256-jrlspTk8eSbhsrt52DJGB/Nz9GniPonpBzfSfcVbagM="}, {"Name": "label", "Value": "styles.css"}]}, {"Route": "styles.mb95wunvkt.css.gz", "AssetFile": "styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4360"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5SsbBNT/tJF3BhUIoOeC6Hpd9FQozc56iUv2Qap7q9I=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mb95wunvkt"}, {"Name": "integrity", "Value": "sha256-5SsbBNT/tJF3BhUIoOeC6Hpd9FQozc56iUv2Qap7q9I="}, {"Name": "label", "Value": "styles.css.gz"}]}, {"Route": "todo-app.js", "AssetFile": "todo-app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000214592275"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4659"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"73F3jtJKw8iLgSygXmbs0RMmml4rdmMezHI3HpY7jiM=\""}, {"Name": "ETag", "Value": "W/\"mlSsPrANl7IrzlrokoEDKJSmbLi4QBbcYdmBQzgO/OE=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mlSsPrANl7IrzlrokoEDKJSmbLi4QBbcYdmBQzgO/OE="}]}, {"Route": "todo-app.js", "AssetFile": "todo-app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "20502"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mlSsPrANl7IrzlrokoEDKJSmbLi4QBbcYdmBQzgO/OE=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:46:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mlSsPrANl7IrzlrokoEDKJSmbLi4QBbcYdmBQzgO/OE="}]}, {"Route": "todo-app.js.gz", "AssetFile": "todo-app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4659"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"73F3jtJKw8iLgSygXmbs0RMmml4rdmMezHI3HpY7jiM=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-73F3jtJKw8iLgSygXmbs0RMmml4rdmMezHI3HpY7jiM="}]}, {"Route": "todo-app.yirdgu4cai.js", "AssetFile": "todo-app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000214592275"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4659"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"73F3jtJKw8iLgSygXmbs0RMmml4rdmMezHI3HpY7jiM=\""}, {"Name": "ETag", "Value": "W/\"mlSsPrANl7IrzlrokoEDKJSmbLi4QBbcYdmBQzgO/OE=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yirdgu4cai"}, {"Name": "integrity", "Value": "sha256-mlSsPrANl7IrzlrokoEDKJSmbLi4QBbcYdmBQzgO/OE="}, {"Name": "label", "Value": "todo-app.js"}]}, {"Route": "todo-app.yirdgu4cai.js", "AssetFile": "todo-app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "20502"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mlSsPrANl7IrzlrokoEDKJSmbLi4QBbcYdmBQzgO/OE=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:46:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yirdgu4cai"}, {"Name": "integrity", "Value": "sha256-mlSsPrANl7IrzlrokoEDKJSmbLi4QBbcYdmBQzgO/OE="}, {"Name": "label", "Value": "todo-app.js"}]}, {"Route": "todo-app.yirdgu4cai.js.gz", "AssetFile": "todo-app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4659"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"73F3jtJKw8iLgSygXmbs0RMmml4rdmMezHI3HpY7jiM=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yirdgu4cai"}, {"Name": "integrity", "Value": "sha256-73F3jtJKw8iLgSygXmbs0RMmml4rdmMezHI3HpY7jiM="}, {"Name": "label", "Value": "todo-app.js.gz"}]}, {"Route": "ui-manager.js", "AssetFile": "ui-manager.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000141683196"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7057"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"S6Y2dIN39rkBvg2Af8QgOB9BTy/W1v1ebCYkn5vqejM=\""}, {"Name": "ETag", "Value": "W/\"aVY1Nq6bMgvQ71moBOwjdYBcuWJcjIAAt1g4+XIsTFE=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aVY1Nq6bMgvQ71moBOwjdYBcuWJcjIAAt1g4+XIsTFE="}]}, {"Route": "ui-manager.js", "AssetFile": "ui-manager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "35289"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"aVY1Nq6bMgvQ71moBOwjdYBcuWJcjIAAt1g4+XIsTFE=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:46:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aVY1Nq6bMgvQ71moBOwjdYBcuWJcjIAAt1g4+XIsTFE="}]}, {"Route": "ui-manager.js.gz", "AssetFile": "ui-manager.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7057"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"S6Y2dIN39rkBvg2Af8QgOB9BTy/W1v1ebCYkn5vqejM=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S6Y2dIN39rkBvg2Af8QgOB9BTy/W1v1ebCYkn5vqejM="}]}, {"Route": "ui-manager.zhln9tevi0.js", "AssetFile": "ui-manager.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000141683196"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7057"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"S6Y2dIN39rkBvg2Af8QgOB9BTy/W1v1ebCYkn5vqejM=\""}, {"Name": "ETag", "Value": "W/\"aVY1Nq6bMgvQ71moBOwjdYBcuWJcjIAAt1g4+XIsTFE=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zhln9tevi0"}, {"Name": "integrity", "Value": "sha256-aVY1Nq6bMgvQ71moBOwjdYBcuWJcjIAAt1g4+XIsTFE="}, {"Name": "label", "Value": "ui-manager.js"}]}, {"Route": "ui-manager.zhln9tevi0.js", "AssetFile": "ui-manager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "35289"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"aVY1Nq6bMgvQ71moBOwjdYBcuWJcjIAAt1g4+XIsTFE=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:46:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zhln9tevi0"}, {"Name": "integrity", "Value": "sha256-aVY1Nq6bMgvQ71moBOwjdYBcuWJcjIAAt1g4+XIsTFE="}, {"Name": "label", "Value": "ui-manager.js"}]}, {"Route": "ui-manager.zhln9tevi0.js.gz", "AssetFile": "ui-manager.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7057"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"S6Y2dIN39rkBvg2Af8QgOB9BTy/W1v1ebCYkn5vqejM=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zhln9tevi0"}, {"Name": "integrity", "Value": "sha256-S6Y2dIN39rkBvg2Af8QgOB9BTy/W1v1ebCYkn5vqejM="}, {"Name": "label", "Value": "ui-manager.js.gz"}]}]}