{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "index.5lbvunm6pn.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001305483029"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "765"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"fL6DYJfZe8Kh5PgiL0pbcziLtCpR2DefJfiZd+wDREc=\""}, {"Name": "ETag", "Value": "W/\"lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 11:42:02 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5lbvunm6pn"}, {"Name": "integrity", "Value": "sha256-lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.5lbvunm6pn.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1941"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 03:04:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5lbvunm6pn"}, {"Name": "integrity", "Value": "sha256-lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.5lbvunm6pn.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "765"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"fL6DYJfZe8Kh5PgiL0pbcziLtCpR2DefJfiZd+wDREc=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 11:42:02 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5lbvunm6pn"}, {"Name": "integrity", "Value": "sha256-fL6DYJfZe8Kh5PgiL0pbcziLtCpR2DefJfiZd+wDREc="}, {"Name": "label", "Value": "index.html.gz"}]}, {"Route": "index.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001305483029"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "765"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"fL6DYJfZe8Kh5PgiL0pbcziLtCpR2DefJfiZd+wDREc=\""}, {"Name": "ETag", "Value": "W/\"lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 11:42:02 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1941"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 03:04:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ="}]}, {"Route": "index.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "765"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"fL6DYJfZe8Kh5PgiL0pbcziLtCpR2DefJfiZd+wDREc=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 11:42:02 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fL6DYJfZe8Kh5PgiL0pbcziLtCpR2DefJfiZd+wDREc="}]}, {"Route": "index9.html", "AssetFile": "index9.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000053064473"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "18844"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"8ZqHjOoNYBO+CDfXncMOnCa/6p75C/Gh01Un++ru/jU=\""}, {"Name": "ETag", "Value": "W/\"FIde/dU7aQUKQtdbytzJ080hCNDsB92jY1QRTZx4xU8=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 11:42:02 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FIde/dU7aQUKQtdbytzJ080hCNDsB92jY1QRTZx4xU8="}]}, {"Route": "index9.html", "AssetFile": "index9.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "118795"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"FIde/dU7aQUKQtdbytzJ080hCNDsB92jY1QRTZx4xU8=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 04:14:16 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FIde/dU7aQUKQtdbytzJ080hCNDsB92jY1QRTZx4xU8="}]}, {"Route": "index9.html.gz", "AssetFile": "index9.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "18844"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"8ZqHjOoNYBO+CDfXncMOnCa/6p75C/Gh01Un++ru/jU=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 11:42:02 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8ZqHjOoNYBO+CDfXncMOnCa/6p75C/Gh01Un++ru/jU="}]}, {"Route": "index9.o7kzmwadhj.html", "AssetFile": "index9.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000053064473"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "18844"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"8ZqHjOoNYBO+CDfXncMOnCa/6p75C/Gh01Un++ru/jU=\""}, {"Name": "ETag", "Value": "W/\"FIde/dU7aQUKQtdbytzJ080hCNDsB92jY1QRTZx4xU8=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 11:42:02 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o7kzmwadhj"}, {"Name": "integrity", "Value": "sha256-FIde/dU7aQUKQtdbytzJ080hCNDsB92jY1QRTZx4xU8="}, {"Name": "label", "Value": "index9.html"}]}, {"Route": "index9.o7kzmwadhj.html", "AssetFile": "index9.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "118795"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"FIde/dU7aQUKQtdbytzJ080hCNDsB92jY1QRTZx4xU8=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 04:14:16 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o7kzmwadhj"}, {"Name": "integrity", "Value": "sha256-FIde/dU7aQUKQtdbytzJ080hCNDsB92jY1QRTZx4xU8="}, {"Name": "label", "Value": "index9.html"}]}, {"Route": "index9.o7kzmwadhj.html.gz", "AssetFile": "index9.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "18844"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"8ZqHjOoNYBO+CDfXncMOnCa/6p75C/Gh01Un++ru/jU=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 11:42:02 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o7kzmwadhj"}, {"Name": "integrity", "Value": "sha256-8ZqHjOoNYBO+CDfXncMOnCa/6p75C/Gh01Un++ru/jU="}, {"Name": "label", "Value": "index9.html.gz"}]}]}