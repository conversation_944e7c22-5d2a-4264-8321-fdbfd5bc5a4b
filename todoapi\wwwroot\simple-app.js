// Simplified API Service - No caching, no offline support
class SimpleApiService {
    constructor() {
        this.baseUrl = '/api';
    }

    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        console.log(`🌐 API Request: ${options.method || 'GET'} ${url}`);
        
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        
        // Handle API response format
        if (data.success === false) {
            throw new Error(data.message || 'API request failed');
        }

        return data.data || data;
    }

    async getTasks() {
        return await this.request('/tasks');
    }

    async createTask(taskData) {
        return await this.request('/tasks', {
            method: 'POST',
            body: JSON.stringify({
                title: taskData.title,
                notes: taskData.notes || null,
                dueDate: taskData.dueDate || null,
                recurrence: taskData.recurrence || null,
                isImportant: taskData.isImportant || false,
                myDayDate: taskData.myDayDate || null,
                listId: taskData.listId || null,
                assigneeIds: taskData.assignees?.map(a => a.id) || []
            })
        });
    }

    async updateTask(taskId, updates) {
        return await this.request(`/tasks/${taskId}`, {
            method: 'PUT',
            body: JSON.stringify({
                title: updates.title,
                notes: updates.notes,
                isCompleted: updates.isCompleted,
                isImportant: updates.isImportant,
                dueDate: updates.dueDate,
                recurrence: updates.recurrence,
                myDayDate: updates.myDayDate,
                listId: updates.listId,
                assigneeIds: updates.assignees?.map(a => a.id)
            })
        });
    }

    async deleteTask(taskId) {
        return await this.request(`/tasks/${taskId}`, {
            method: 'DELETE'
        });
    }

    async getLists() {
        return await this.request('/lists');
    }

    async createList(listData) {
        return await this.request('/lists', {
            method: 'POST',
            body: JSON.stringify({
                name: listData.name,
                icon: listData.icon || '📁'
            })
        });
    }

    async deleteList(listId) {
        return await this.request(`/lists/${listId}`, {
            method: 'DELETE'
        });
    }

    async getUsers() {
        return await this.request('/users');
    }
}

// Connection Status Manager
class ConnectionManager {
    constructor() {
        this.isOnline = navigator.onLine;
        this.statusElement = null;
        this.init();
    }

    init() {
        // Create connection status indicator
        this.createStatusIndicator();
        
        // Listen for online/offline events
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.updateStatus();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.updateStatus();
        });

        // Initial status update
        this.updateStatus();
    }

    createStatusIndicator() {
        this.statusElement = document.createElement('div');
        this.statusElement.id = 'connection-status';
        this.statusElement.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background-color: #d32f2f;
            color: white;
            text-align: center;
            padding: 10px;
            font-weight: bold;
            z-index: 10000;
            display: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        `;
        this.statusElement.innerHTML = '⚠️ No Internet Connection - App requires internet to function';
        document.body.appendChild(this.statusElement);
    }

    updateStatus() {
        if (this.isOnline) {
            this.statusElement.style.display = 'none';
            document.body.style.paddingTop = '0';
        } else {
            this.statusElement.style.display = 'block';
            document.body.style.paddingTop = '50px';
        }
    }

    checkConnection() {
        if (!this.isOnline) {
            throw new Error('No internet connection available');
        }
    }
}

// Simplified Todo App - No offline support
class SimpleTodoApp {
    constructor() {
        this.api = new SimpleApiService();
        this.connectionManager = new ConnectionManager();
        this.state = {
            tasks: [],
            lists: [],
            users: [],
            theme: 'light',
            searchTerm: '',
            sortBy: 'created-desc',
            currentList: 'all'
        };
        this.ui = new SimpleUIManager(this);
    }

    async init() {
        console.log('🚀 Initializing Simple Todo App...');
        
        // Set up theme
        document.documentElement.setAttribute('data-theme', this.state.theme);
        
        try {
            // Check connection before loading
            this.connectionManager.checkConnection();
            
            // Load data from server
            await this.loadData();
            
            // Render UI
            this.ui.render();
            
            console.log('✅ App initialized successfully');
        } catch (error) {
            console.error('❌ Failed to initialize app:', error);
            this.ui.showError('Failed to load data. Please check your internet connection.');
        }
    }

    async loadData() {
        console.log('📡 Loading data from server...');
        
        const [tasks, lists, users] = await Promise.all([
            this.api.getTasks(),
            this.api.getLists(),
            this.api.getUsers()
        ]);

        this.state.tasks = Array.isArray(tasks) ? tasks : [];
        this.state.lists = Array.isArray(lists) ? lists : [];
        this.state.users = Array.isArray(users) ? users : [];

        console.log('✅ Data loaded:', {
            tasks: this.state.tasks.length,
            lists: this.state.lists.length,
            users: this.state.users.length
        });
    }

    async addTask(taskData) {
        try {
            this.connectionManager.checkConnection();
            
            const listId = this.state.currentList.startsWith('list-') ? 
                this.state.currentList.replace('list-', '') : null;
            const isImportant = this.state.currentList === 'important';
            const myDayDate = this.state.currentList === 'my-day' ? new Date().toDateString() : null;

            const fullTaskData = { ...taskData, listId, isImportant, myDayDate };
            
            const newTask = await this.api.createTask(fullTaskData);
            this.state.tasks.push(newTask);
            this.ui.render();
            
            console.log('✅ Task created:', newTask.title);
        } catch (error) {
            console.error('❌ Failed to create task:', error);
            this.ui.showError('Failed to create task. Please check your internet connection.');
        }
    }

    async updateTask(taskId, updates) {
        try {
            this.connectionManager.checkConnection();
            
            const updatedTask = await this.api.updateTask(taskId, updates);
            const index = this.state.tasks.findIndex(t => t.id === taskId);
            if (index !== -1) {
                this.state.tasks[index] = updatedTask;
            }
            this.ui.render();
            
            console.log('✅ Task updated:', taskId);
        } catch (error) {
            console.error('❌ Failed to update task:', error);
            this.ui.showError('Failed to update task. Please check your internet connection.');
        }
    }

    async deleteTask(taskId) {
        try {
            this.connectionManager.checkConnection();
            
            await this.api.deleteTask(taskId);
            this.state.tasks = this.state.tasks.filter(t => t.id !== taskId);
            this.ui.render();
            
            console.log('✅ Task deleted:', taskId);
        } catch (error) {
            console.error('❌ Failed to delete task:', error);
            this.ui.showError('Failed to delete task. Please check your internet connection.');
        }
    }

    async duplicateTask(taskId) {
        try {
            this.connectionManager.checkConnection();
            
            const originalTask = this.state.tasks.find(t => t.id === taskId);
            if (!originalTask) return;

            const duplicatedTask = {
                title: `${originalTask.title} (Copy)`,
                notes: originalTask.notes,
                dueDate: originalTask.dueDate,
                recurrence: originalTask.recurrence,
                isImportant: originalTask.isImportant,
                assignees: originalTask.assignees
            };

            const newTask = await this.api.createTask(duplicatedTask);
            this.state.tasks.push(newTask);
            this.ui.render();
            
            console.log('✅ Task duplicated:', originalTask.title);
        } catch (error) {
            console.error('❌ Failed to duplicate task:', error);
            this.ui.showError('Failed to duplicate task. Please check your internet connection.');
        }
    }

    async createList(listData) {
        try {
            this.connectionManager.checkConnection();
            
            const newList = await this.api.createList(listData);
            this.state.lists.push(newList);
            this.ui.render();
            
            console.log('✅ List created:', newList.name);
        } catch (error) {
            console.error('❌ Failed to create list:', error);
            this.ui.showError('Failed to create list. Please check your internet connection.');
        }
    }

    async deleteList(listId) {
        try {
            this.connectionManager.checkConnection();
            
            await this.api.deleteList(listId);
            this.state.lists = this.state.lists.filter(l => l.id !== listId);
            
            // Move tasks from deleted list to "All Tasks"
            this.state.tasks.forEach(task => {
                if (task.listId === listId) {
                    task.listId = null;
                }
            });
            
            this.ui.render();
            
            console.log('✅ List deleted:', listId);
        } catch (error) {
            console.error('❌ Failed to delete list:', error);
            this.ui.showError('Failed to delete list. Please check your internet connection.');
        }
    }

    // Helper methods
    getFilteredTasks() {
        let filtered = this.state.tasks;

        // Filter by current list
        switch (this.state.currentList) {
            case 'my-day':
                const today = new Date().toDateString();
                filtered = filtered.filter(task => task.myDayDate === today);
                break;
            case 'important':
                filtered = filtered.filter(task => task.isImportant);
                break;
            case 'planned':
                filtered = filtered.filter(task => task.dueDate);
                break;
            default:
                if (this.state.currentList.startsWith('user-')) {
                    const userId = this.state.currentList.replace('user-', '');
                    filtered = filtered.filter(task => 
                        task.assignees?.some(assignee => assignee.id === userId)
                    );
                } else if (this.state.currentList.startsWith('list-')) {
                    const listId = this.state.currentList.replace('list-', '');
                    filtered = filtered.filter(task => task.listId === listId);
                }
        }

        // Filter by search term
        if (this.state.searchTerm) {
            const term = this.state.searchTerm.toLowerCase();
            filtered = filtered.filter(task => 
                task.title.toLowerCase().includes(term) ||
                (task.notes && task.notes.toLowerCase().includes(term))
            );
        }

        // Apply sorting
        filtered.sort((a, b) => {
            switch (this.state.sortBy) {
                case 'title-asc':
                    return a.title.localeCompare(b.title);
                case 'title-desc':
                    return b.title.localeCompare(a.title);
                case 'created-desc':
                    return new Date(b.createdAt) - new Date(a.createdAt);
                case 'created-asc':
                    return new Date(a.createdAt) - new Date(b.createdAt);
                case 'due-asc':
                    if (!a.dueDate && !b.dueDate) return 0;
                    if (!a.dueDate) return 1;
                    if (!b.dueDate) return -1;
                    return new Date(a.dueDate) - new Date(b.dueDate);
                case 'due-desc':
                    if (!a.dueDate && !b.dueDate) return 0;
                    if (!a.dueDate) return -1;
                    if (!b.dueDate) return 1;
                    return new Date(b.dueDate) - new Date(a.dueDate);
                case 'importance-desc':
                    return (b.isImportant ? 1 : 0) - (a.isImportant ? 1 : 0);
                case 'completion-asc':
                    return (a.isCompleted ? 1 : 0) - (b.isCompleted ? 1 : 0);
                case 'completion-desc':
                    return (b.isCompleted ? 1 : 0) - (a.isCompleted ? 1 : 0);
                default:
                    return 0;
            }
        });

        return filtered;
    }

    switchList(listId) {
        this.state.currentList = listId;
        this.ui.render();
    }

    toggleTheme() {
        this.state.theme = this.state.theme === 'light' ? 'dark' : 'light';
        document.documentElement.setAttribute('data-theme', this.state.theme);
        this.ui.updateThemeToggle();
    }
}
