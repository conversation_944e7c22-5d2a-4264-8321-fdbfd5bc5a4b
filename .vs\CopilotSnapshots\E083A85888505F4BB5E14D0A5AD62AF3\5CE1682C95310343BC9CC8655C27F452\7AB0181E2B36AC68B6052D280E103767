﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Todo App</title>
    <style>
        :root {
            /* Light theme */
            --primary-color: #0078d4;
            --background-primary: #ffffff;
            --background-secondary: #f3f2f1;
            --text-primary: #323130;
            --text-secondary: #605e5c;
            --surface-border: #edebe9;
            --surface-hover: #f3f2f1;
            --success-color: #107c10;
            --warning-color: #ff8c00;
            --error-color: #d13438;
        }

        [data-theme="dark"] {
            --primary-color: #0086f0;
            --background-primary: #1e1e1e;
            --background-secondary: #2d2c2c;
            --text-primary: #ffffff;
            --text-secondary: #b3b0ad;
            --surface-border: #3b3a39;
            --surface-hover: #3b3a39;
            --success-color: #54b054;
            --warning-color: #ffaa44;
            --error-color: #f85149;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--background-primary);
            color: var(--text-primary);
            line-height: 1.5;
            transition: background-color 0.3s, color 0.3s;
        }

        .app-container {
            display: flex;
            height: 100vh;
            max-width: 1400px;
            margin: 0 auto;
        }

        /* Sidebar */
        .sidebar {
            width: 280px;
            background-color: var(--background-secondary);
            border-right: 1px solid var(--surface-border);
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        .app-header {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 30px;
            row-gap: 15px;
        }

        .app-title {
            font-size: 24px;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0;
        }

        .search-container {
            position: relative;
            margin-bottom: 0;
            flex-basis: 100%;
        }

        .search-input {
            width: 100%;
            padding: 10px 12px 10px 36px;
            border: 1px solid var(--surface-border);
            border-radius: 6px;
            background-color: var(--background-primary);
            color: var(--text-primary);
            font-size: 14px;
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
        }

        .list-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            color: var(--text-secondary);
            margin-bottom: 12px;
            letter-spacing: 0.5px;
        }

        .list-item, .user-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s;
            margin-bottom: 2px;
            position: relative;
        }

        .list-item:hover, .user-item:hover {
            background-color: var(--surface-hover);
        }

        .list-item.active, .user-item.active {
            background-color: var(--primary-color);
            color: white;
        }

        .list-item .icon, .user-item .icon {
            margin-right: 12px;
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        .list-item span:nth-child(2), .user-item span:nth-child(2) {
            flex: 1;
            font-size: 14px;
        }

        .list-count {
            font-size: 12px;
            background-color: var(--surface-border);
            color: var(--text-secondary);
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 20px;
            text-align: center;
        }

        .list-item.active .list-count {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .new-list-form {
            margin-top: 8px;
        }

        .new-list-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid transparent;
            border-radius: 6px;
            background: transparent;
            color: var(--text-secondary);
            font-size: 14px;
            transition: all 0.2s;
        }

        .new-list-input:hover {
            background-color: var(--surface-hover);
        }

        .new-list-input:focus {
            outline: none;
            border-color: var(--primary-color);
            background-color: var(--background-primary);
            color: var(--text-primary);
        }

        .list-delete {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            opacity: 0;
            transition: opacity 0.2s;
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
        }

        .list-item:hover .list-delete {
            opacity: 1;
        }

        .list-delete:hover {
            background-color: var(--surface-hover);
            color: #d13438;
        }

        .user-avatar {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 12px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: white;
            flex-shrink: 0;
        }

        /* Main Content */
        .content {
            flex: 1;
            padding: 20px 30px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        .list-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 16px;
        }
        
        .header-controls {
            display: flex;
            align-items: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .sort-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .sort-label {
            font-size: 12px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .sort-select {
            padding: 6px 8px;
            border: 1px solid var(--surface-border);
            border-radius: 4px;
            background-color: var(--background-secondary);
            color: var(--text-primary);
            font-size: 12px;
            cursor: pointer;
            min-width: 160px;
        }

        .sort-select:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .sort-select:hover {
            background-color: var(--surface-hover);
        }

        .toolbar {
            display: flex;
            gap: 8px;
        }

        .toolbar-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 4px;
            padding: 8px;
            background-color: var(--background-secondary);
            border: 1px solid var(--surface-border);
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s, border-color 0.2s;
            font-family: inherit;
            color: var(--text-secondary);
            min-width: 70px;
            height: 60px;
            text-align: center;
        }

        .toolbar-btn:hover {
            background-color: var(--surface-hover);
            border-color: var(--primary-color);
        }

        .toolbar-icon {
            font-size: 20px;
            line-height: 1;
        }

        .toolbar-label {
            font-size: 12px;
            font-weight: 500;
            line-height: 1.1;
        }

        .list-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .task-stats {
            color: var(--text-secondary);
            font-size: 14px;
        }

        .analytics-panel {
            background: var(--background-secondary);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
            border: 1px solid var(--surface-border);
        }

        .analytics-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--text-primary);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 12px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 20px;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .tasks {
            margin-bottom: 20px;
            flex: 1;
            overflow-y: auto;
        }

        .task-item {
            display: flex;
            align-items: flex-start;
            padding: 12px 16px;
            background-color: var(--background-primary);
            border: 1px solid var(--surface-border);
            border-radius: 8px;
            margin-bottom: 8px;
            transition: all 0.2s;
            position: relative;
        }

        .task-item:hover {
            border-color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(0, 120, 212, 0.1);
        }

        .task-item.completed {
            opacity: 0.6;
        }

        .task-item.completed .task-title {
            text-decoration: line-through;
        }

        .task-item.important {
            border-left: 4px solid var(--warning-color);
        }

        .task-item.selected {
            background-color: rgba(0, 120, 212, 0.1);
            border-color: var(--primary-color);
        }

        .task-checkbox {
            width: 20px;
            height: 20px;
            border: 2px solid var(--surface-border);
            border-radius: 50%;
            background: none;
            cursor: pointer;
            margin-right: 12px;
            margin-top: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            flex-shrink: 0;
        }

        .task-checkbox:hover {
            border-color: var(--primary-color);
        }

        .task-item.completed .task-checkbox {
            background-color: var(--success-color);
            border-color: var(--success-color);
            color: white;
        }
        
        .task-item .task-select {
            position: absolute;
            right: 10px;
            top: 10px;
        }

        .task-content {
            flex: 1;
            cursor: pointer;
        }

        .task-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
        }

        .task-notes {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .task-meta {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: var(--text-secondary);
            flex-wrap: wrap;
        }

        .task-assignees {
            display: flex;
            gap: 4px;
            margin-top: 8px;
        }

        .assignee-avatar {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: white;
        }

        .task-actions {
            display: flex;
            gap: 4px;
            margin-left: 12px;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .task-item:hover .task-actions {
            opacity: 1;
        }

        .task-important, .task-delete, .task-edit, .task-duplicate {
            background: none;
            border: none;
            padding: 6px;
            border-radius: 4px;
            cursor: pointer;
            color: var(--text-secondary);
            transition: all 0.2s;
        }

        .task-important:hover {
            background-color: var(--surface-hover);
            color: var(--warning-color);
        }

        .task-important.active {
            color: var(--warning-color);
        }

        .task-delete:hover {
            background-color: var(--surface-hover);
            color: var(--error-color);
        }

        .task-edit:hover {
            background-color: var(--surface-hover);
            color: var(--primary-color);
        }

        .task-duplicate:hover {
            background-color: var(--surface-hover);
            color: var(--primary-color);
        }

        /* Task Action Tooltips */
        .task-action-btn {
            position: relative;
        }

        .task-action-btn::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background-color: var(--text-primary);
            color: var(--background-primary);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s, visibility 0.2s;
            z-index: 1000;
            pointer-events: none;
            margin-bottom: 4px;
        }

        .task-action-btn:hover::after {
            opacity: 1;
            visibility: visible;
        }

        /* Arrow for tooltip */
        .task-action-btn::before {
            content: '';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 4px solid transparent;
            border-top-color: var(--text-primary);
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s, visibility 0.2s;
            z-index: 1000;
            pointer-events: none;
        }

        .task-action-btn:hover::before {
            opacity: 1;
            visibility: visible;
        }

        .task-input {
            background-color: var(--background-primary);
            border-top: 1px solid var(--surface-border);
            padding: 16px 0 0;
            margin-top: auto;
        }

        .task-input-form {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .new-task-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid var(--surface-border);
            border-radius: 6px;
            background-color: var(--background-secondary);
            color: var(--text-primary);
            font-size: 14px;
        }

        .new-task-date {
            padding: 12px 16px;
            border: 1px solid var(--surface-border);
            border-radius: 6px;
            background-color: var(--background-secondary);
            color: var(--text-primary);
            font-size: 14px;
            width: 150px;
        }

        .new-task-input:focus, .new-task-date:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .add-task-button {
            padding: 12px 20px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .add-task-button:hover {
            background-color: #106ebe;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }

        .empty-state .icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .empty-state h3 {
            font-size: 18px;
            margin-bottom: 8px;
            color: var(--text-primary);
        }

        .theme-toggle {
            background-color: transparent;
            color: var(--text-secondary);
            border: 1px solid var(--surface-border);
            border-radius: 6px;
            width: 36px;
            height: 36px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .theme-toggle:hover {
            background-color: var(--surface-hover);
        }

        .header-buttons {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .help-button {
            background-color: transparent;
            color: var(--text-secondary);
            border: 1px solid var(--surface-border);
            border-radius: 6px;
            width: 36px;
            height: 36px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .help-button:hover {
            background-color: var(--surface-hover);
            color: var(--primary-color);
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .modal-overlay.show {
            display: flex;
        }

        .modal {
            background: var(--background-primary);
            border-radius: 8px;
            padding: 24px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: var(--text-secondary);
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            margin-bottom: 6px;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-input, .form-textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid var(--surface-border);
            border-radius: 6px;
            background-color: var(--background-secondary);
            color: var(--text-primary);
            font-size: 14px;
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .modal-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 24px;
        }
        
        /* Custom Assignee Select in Modal */
        .assignee-list {
            max-height: 150px;
            overflow-y: auto;
            border: 1px solid var(--surface-border);
            border-radius: 6px;
            background-color: var(--background-secondary);
        }

        .assignee-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            cursor: pointer;
            transition: background-color 0.2s;
            user-select: none;
        }
        
        .assignee-item:hover {
            background-color: var(--surface-hover);
        }

        .assignee-item .user-avatar {
            margin-right: 12px;
        }

        .assignee-item span:not(.checkmark) {
            flex: 1;
        }
        
        .assignee-item .checkmark {
            font-size: 16px;
            color: var(--primary-color);
            font-weight: bold;
            width: 20px;
            text-align: center;
        }
        
        [data-theme="dark"] .assignee-item.selected {
             background-color: rgba(0, 134, 240, 0.2);
        }

        [data-theme="light"] .assignee-item.selected {
            background-color: rgba(0, 120, 212, 0.1);
        }

        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: #106ebe;
        }

        .btn-secondary {
            background-color: var(--surface-border);
            color: var(--text-primary);
        }

        .btn-secondary:hover {
            background-color: var(--surface-hover);
        }

        /* Bulk Actions */
        .bulk-actions {
            display: none;
            background: var(--background-secondary);
            border: 1px solid var(--surface-border);
            border-radius: 8px;
            padding: 12px 16px;
            margin-bottom: 16px;
            align-items: center;
            gap: 12px;
        }

        .bulk-actions.show {
            display: flex;
        }

        .bulk-info {
            flex: 1;
            font-size: 14px;
            color: var(--text-primary);
        }

        .bulk-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .bulk-complete {
            background-color: var(--success-color);
            color: white;
        }

        .bulk-delete {
            background-color: var(--error-color);
            color: white;
        }

        .bulk-cancel {
            background-color: var(--surface-border);
            color: var(--text-primary);
        }
        
        .overdue {
            color: var(--error-color);
        }

        /* Drag and Drop Styles */
        .task-item[draggable="true"] {
            cursor: grab;
        }

        .task-item.dragging {
            opacity: 0.5;
            transform: rotate(2deg);
            cursor: grabbing;
            z-index: 1000;
        }

        .task-item.drag-over {
            border-top: 3px solid var(--primary-color);
            margin-top: 3px;
        }

        .list-item.drag-over {
            background-color: rgba(0, 120, 212, 0.1);
            border: 2px dashed var(--primary-color);
        }

        .drop-zone {
            min-height: 40px;
            border: 2px dashed transparent;
            border-radius: 8px;
            margin: 4px 0;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .drop-zone.active {
            border-color: var(--primary-color);
            background-color: rgba(0, 120, 212, 0.05);
            color: var(--primary-color);
        }

        .drag-handle {
            cursor: grab;
            color: var(--text-secondary);
            padding: 4px;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .task-item:hover .drag-handle {
            opacity: 1;
        }

        .drag-handle:active {
            cursor: grabbing;
        }

        /* Keyboard Shortcuts Modal Styles */
        .shortcuts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .shortcut-section h4 {
            color: var(--primary-color);
            margin-bottom: 12px;
            font-size: 14px;
            font-weight: 600;
        }

        .shortcut-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 0;
            border-bottom: 1px solid var(--surface-border);
        }

        .shortcut-item:last-child {
            border-bottom: none;
        }

        .shortcut-item kbd {
            background-color: var(--background-secondary);
            border: 1px solid var(--surface-border);
            border-radius: 4px;
            padding: 2px 6px;
            font-size: 11px;
            font-family: monospace;
            color: var(--text-primary);
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .shortcut-item span {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .shortcuts-note {
            background-color: var(--background-secondary);
            border-radius: 6px;
            padding: 12px;
            margin-top: 16px;
        }

        .shortcuts-note p {
            margin: 0;
            font-size: 13px;
            color: var(--text-secondary);
        }

        /* Conflict Resolution Modal Styles */
        .conflict-modal {
            max-width: 800px;
            width: 90vw;
        }

        .conflict-description {
            background-color: var(--background-secondary);
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 20px;
            border-left: 4px solid var(--warning-color);
        }

        .conflict-comparison {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 20px;
            margin-bottom: 24px;
            align-items: start;
        }

        .conflict-side h4 {
            margin: 0 0 12px 0;
            font-size: 14px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .conflict-data {
            background-color: var(--background-secondary);
            border-radius: 6px;
            padding: 16px;
            border: 1px solid var(--surface-border);
        }

        .data-preview {
            font-family: monospace;
            font-size: 12px;
            color: var(--text-secondary);
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-word;
        }

        .data-timestamp {
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid var(--surface-border);
            font-size: 11px;
            color: var(--text-tertiary);
        }

        .conflict-divider {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
        }

        .vs-indicator {
            background-color: var(--primary-color);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 12px;
        }

        .resolution-options h4 {
            margin: 0 0 16px 0;
            font-size: 14px;
            color: var(--text-primary);
        }

        .resolution-buttons {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .resolution-buttons .btn {
            flex: 1;
            min-width: 160px;
            padding: 12px 16px;
            font-size: 13px;
            transition: all 0.2s;
        }

        .resolution-buttons .btn.selected {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .auto-resolution {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid var(--surface-border);
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-size: 13px;
            color: var(--text-secondary);
        }

        .checkbox-label input[type="checkbox"] {
            margin: 0;
        }

        /* Sync Status Indicator */
        .sync-status {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: var(--background-primary);
            border: 1px solid var(--surface-border);
            border-radius: 8px;
            padding: 8px 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 12px;
            z-index: 1000;
        }

        .sync-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .sync-icon {
            font-size: 14px;
        }

        .sync-text {
            color: var(--text-secondary);
        }

        .conflict-badge {
            background-color: var(--error-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .conflict-badge:hover {
            background-color: var(--error-hover);
        }

        .sync-status.syncing .sync-icon {
            animation: spin 1s linear infinite;
        }

        .sync-status.error .sync-icon {
            color: var(--error-color);
        }

        .sync-status.success .sync-icon {
            color: var(--success-color);
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Mobile responsiveness for conflict modal */
        @media (max-width: 768px) {
            .conflict-modal {
                width: 95vw;
                max-width: none;
            }

            .conflict-comparison {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .conflict-divider {
                padding: 10px 0;
            }

            .vs-indicator {
                transform: rotate(90deg);
            }

            .resolution-buttons {
                flex-direction: column;
            }

            .resolution-buttons .btn {
                min-width: auto;
            }
        }

        @media (max-width: 768px) {
            .app-container {
                flex-direction: column;
                height: auto;
                min-height: 100vh;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
                max-height: 40vh;
                border-right: none;
                border-bottom: 1px solid var(--surface-border);
            }
            
            .content {
                padding: 15px;
            }

            .new-task-date {
                width: auto;
            }

            .task-input-form {
                flex-wrap: wrap;
            }
            
            .new-task-input {
                flex-basis: 100%;
            }
        }
        
        /* Print Styles */
        @media print {
            body, html {
                background: #fff !important;
                color: #000 !important;
            }
            .sidebar, .task-input, .list-header, .analytics-panel, .bulk-actions, .task-actions, .task-checkbox, .task-select, .theme-toggle, .search-container, .list-section {
                display: none !important;
            }
            .app-container, .content, body {
                display: block !important;
                padding: 0 !important;
                margin: 0 !important;
                border: none !important;
                height: auto !important;
                overflow: visible !important;
                box-shadow: none !important;
            }
            .tasks {
                margin: 0;
                padding: 0;
                overflow: visible !important;
            }
            .task-item {
                border: 1px solid #ccc !important;
                box-shadow: none !important;
                page-break-inside: avoid;
            }
            .task-item.completed .task-title {
                color: #888;
            }
            body::before {
                content: 'Todo List: ' attr(data-list-title);
                display: block;
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 20px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <aside class="sidebar" aria-labelledby="app-title">
            <div class="app-header">
                <h1 id="app-title" class="app-title">Todo App</h1>
                <div class="header-buttons">
                    <button class="theme-toggle task-action-btn" id="theme-toggle" data-tooltip="Toggle Dark/Light Theme" aria-label="Toggle theme">🌙</button>
                    <button class="help-button task-action-btn" id="help-button" data-tooltip="Keyboard Shortcuts (?)" aria-label="Show keyboard shortcuts">❓</button>
                </div>
                <div class="search-container">
                    <input type="search" class="search-input" id="search-input" placeholder="Search tasks..." aria-label="Search tasks">
                    <span class="search-icon" aria-hidden="true">🔍</span>
                </div>
            </div>

            <nav class="list-section" aria-labelledby="lists-section-title">
                <h2 id="lists-section-title" class="section-title">Lists</h2>
                <div id="smart-lists-container">
                    <div class="list-item active" data-list="all" role="button" tabindex="0">
                        <span class="icon" aria-hidden="true">📋</span>
                        <span>All Tasks</span>
                        <span class="list-count" id="all-count">0</span>
                    </div>
                    <div class="list-item" data-list="my-day" role="button" tabindex="0">
                        <span class="icon" aria-hidden="true">☀️</span>
                        <span>My Day</span>
                        <span class="list-count" id="my-day-count">0</span>
                    </div>
                    <div class="list-item" data-list="important" role="button" tabindex="0">
                        <span class="icon" aria-hidden="true">⭐</span>
                        <span>Important</span>
                        <span class="list-count" id="important-count">0</span>
                    </div>
                    <div class="list-item" data-list="planned" role="button" tabindex="0">
                        <span class="icon" aria-hidden="true">📅</span>
                        <span>Planned</span>
                        <span class="list-count" id="planned-count">0</span>
                    </div>
                </div>
                
                <!-- Custom Lists Container -->
                <div id="custom-lists"></div>
                
                <!-- Add List Form -->
                <form id="new-list-form" class="new-list-form" role="search">
                    <input type="text" id="new-list-input" placeholder="+ New list" class="new-list-input" aria-label="Create new list">
                </form>
            </nav>

            <div class="list-section" aria-labelledby="users-section-title">
                <h2 id="users-section-title" class="section-title">Users</h2>
                <div id="users-container">
                    <!-- Users will be rendered here -->
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="content" aria-labelledby="list-title">
            <header class="list-header">
                <div>
                    <h2 class="list-title" id="list-title">All Tasks</h2>
                    <div class="task-stats" id="task-count" aria-live="polite">0 tasks</div>
                </div>
                <div class="header-controls">
                    <div class="sort-controls">
                        <label for="sort-select" class="sort-label">Sort by:</label>
                        <select id="sort-select" class="sort-select task-action-btn" data-tooltip="Sort Tasks" aria-label="Sort tasks">
                            <option value="created-desc">Date Created (Newest)</option>
                            <option value="created-asc">Date Created (Oldest)</option>
                            <option value="title-asc">Title (A-Z)</option>
                            <option value="title-desc">Title (Z-A)</option>
                            <option value="due-asc">Due Date (Earliest)</option>
                            <option value="due-desc">Due Date (Latest)</option>
                            <option value="importance-desc">Important First</option>
                            <option value="completion-asc">Incomplete First</option>
                            <option value="completion-desc">Completed First</option>
                        </select>
                    </div>
                    <div class="toolbar" id="toolbar" role="toolbar" aria-label="Main actions">
                        <button class="toolbar-btn task-action-btn" id="save-btn" data-tooltip="Save Data to File" aria-label="Save data">
                            <span class="toolbar-icon">💾</span>
                            <span class="toolbar-label">Save</span>
                        </button>
                        <button class="toolbar-btn task-action-btn" id="load-btn" data-tooltip="Load Data from File" aria-label="Load data">
                            <span class="toolbar-icon">📁</span>
                            <span class="toolbar-label">Load</span>
                        </button>
                        <button class="toolbar-btn task-action-btn" id="export-csv-btn" data-tooltip="Export Tasks as CSV" aria-label="Export as CSV">
                            <span class="toolbar-icon">📤</span>
                            <span class="toolbar-label">Export<br>CSV</span>
                        </button>
                        <button class="toolbar-btn task-action-btn" id="print-btn" data-tooltip="Print Current Tasks" aria-label="Print tasks">
                            <span class="toolbar-icon">🖨️</span>
                            <span class="toolbar-label">Print</span>
                        </button>
                        <button class="toolbar-btn task-action-btn" id="clear-btn" data-tooltip="Clear All Tasks in View" aria-label="Clear tasks">
                            <span class="toolbar-icon">🗑️</span>
                            <span class="toolbar-label">Clear</span>
                        </button>
                    </div>
                </div>
            </header>

            <!-- Bulk Actions -->
            <div class="bulk-actions" id="bulk-actions">
                <div class="bulk-info" id="bulk-info">0 tasks selected</div>
                <button class="bulk-btn bulk-complete" id="bulk-complete">Complete</button>
                <button class="bulk-btn bulk-delete" id="bulk-delete">Delete</button>
                <button class="bulk-btn bulk-cancel" id="bulk-cancel">Cancel</button>
            </div>
            
            <div class="analytics-panel" id="analytics-panel">
                <div class="analytics-title">Task Overview</div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="stat-total">0</div>
                        <div class="stat-label">Total</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="stat-completed">0</div>
                        <div class="stat-label">Completed</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="stat-important">0</div>
                        <div class="stat-label">Important</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="stat-overdue">0</div>
                        <div class="stat-label">Overdue</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="stat-today">0</div>
                        <div class="stat-label">Done Today</div>
                    </div>
                </div>
            </div>

            <div class="tasks" id="task-list">
                <!-- Tasks will be rendered here -->
            </div>

            <div class="task-input">
                <form class="task-input-form" id="task-form">
                    <input 
                        type="text" 
                        class="new-task-input" 
                        id="new-task-input"
                        placeholder="Add a task..."
                        autocomplete="off"
                        aria-label="New task title"
                        required
                    >
                    <input 
                        type="date" 
                        class="new-task-date" 
                        id="new-task-date"
                        aria-label="Due date"
                    >
                    <button type="submit" class="add-task-button">Add</button>
                </form>
            </div>
        </main>
    </div>

    <!-- Edit Task Modal -->
    <div class="modal-overlay" id="edit-modal-overlay" role="dialog" aria-modal="true" aria-labelledby="edit-modal-title">
        <div class="modal">
            <header class="modal-header">
                <h3 class="modal-title" id="edit-modal-title">Edit Task</h3>
                <button class="modal-close" id="edit-modal-close" aria-label="Close modal">×</button>
            </header>
            
            <div class="form-group">
                <label class="form-label" for="edit-task-title">Title</label>
                <input type="text" class="form-input" id="edit-task-title" placeholder="Task title">
            </div>

            <div class="form-group">
                <label class="form-label" for="edit-task-notes">Notes</label>
                <textarea class="form-textarea" id="edit-task-notes" placeholder="Add notes..."></textarea>
            </div>

            <div class="form-group">
                <label class="form-label" for="edit-task-due-date">Due Date</label>
                <input type="date" class="form-input" id="edit-task-due-date">
            </div>

            <div class="form-group">
                <label class="form-label" for="edit-task-recurrence">Recurrence</label>
                <select class="form-input" id="edit-task-recurrence">
                    <option value="">No recurrence</option>
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                    <option value="yearly">Yearly</option>
                </select>
            </div>

            <div class="form-group">
                <label class="form-label" for="edit-assignee-list">Assignees</label>
                <div class="assignee-list" id="edit-assignee-list" role="listbox" aria-multiselectable="true">
                    <!-- Assignees will be populated dynamically -->
                </div>
            </div>
            
            <footer class="modal-actions">
                <button class="btn btn-secondary" id="edit-cancel">Cancel</button>
                <button class="btn btn-primary" id="edit-save">Save</button>
            </footer>
        </div>
    </div>

    <!-- Keyboard Shortcuts Help Modal -->
    <div class="modal-overlay" id="shortcuts-modal-overlay" style="display: none;">
        <div class="modal">
            <div class="modal-header">
                <h3>⌨️ Keyboard Shortcuts</h3>
                <button class="modal-close" id="shortcuts-modal-close" aria-label="Close modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="shortcuts-grid">
                    <div class="shortcut-section">
                        <h4>General</h4>
                        <div class="shortcut-item">
                            <kbd>Ctrl/Cmd + N</kbd>
                            <span>New task</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl/Cmd + F</kbd>
                            <span>Search tasks</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl/Cmd + S</kbd>
                            <span>Save data</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl/Cmd + T</kbd>
                            <span>Toggle theme</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Escape</kbd>
                            <span>Close modal/Cancel</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>?</kbd>
                            <span>Show this help</span>
                        </div>
                    </div>
                    <div class="shortcut-section">
                        <h4>Navigation</h4>
                        <div class="shortcut-item">
                            <kbd>1</kbd>
                            <span>All Tasks</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>2</kbd>
                            <span>My Day</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>3</kbd>
                            <span>Important</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>4</kbd>
                            <span>Planned</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>/</kbd>
                            <span>Focus search</span>
                        </div>
                    </div>
                    <div class="shortcut-section">
                        <h4>Task Actions</h4>
                        <div class="shortcut-item">
                            <kbd>Space/Enter</kbd>
                            <span>Toggle complete</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>I</kbd>
                            <span>Toggle important</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>E</kbd>
                            <span>Edit task</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>D</kbd>
                            <span>Delete task</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>C</kbd>
                            <span>Duplicate task</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl/Cmd + B</kbd>
                            <span>Bulk select mode</span>
                        </div>
                    </div>
                </div>
                <div class="shortcuts-note">
                    <p><strong>Tip:</strong> Click on any task to focus it, then use task action shortcuts.</p>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="shortcuts-close">Got it!</button>
            </div>
        </div>
    </div>

    <!-- Conflict Resolution Modal -->
    <div class="modal-overlay" id="conflict-modal-overlay" style="display: none;">
        <div class="modal conflict-modal">
            <div class="modal-header">
                <h3>🔄 Data Conflict Detected</h3>
                <button class="modal-close" id="conflict-modal-close" aria-label="Close modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="conflict-description">
                    <p id="conflict-description-text">Your data has been modified on another device. Please choose how to resolve this conflict.</p>
                </div>

                <div class="conflict-comparison">
                    <div class="conflict-side">
                        <h4>📱 Your Local Changes</h4>
                        <div class="conflict-data" id="local-data">
                            <div class="data-preview"></div>
                            <div class="data-timestamp"></div>
                        </div>
                    </div>

                    <div class="conflict-divider">
                        <div class="vs-indicator">VS</div>
                    </div>

                    <div class="conflict-side">
                        <h4>☁️ Server Changes</h4>
                        <div class="conflict-data" id="server-data">
                            <div class="data-preview"></div>
                            <div class="data-timestamp"></div>
                        </div>
                    </div>
                </div>

                <div class="resolution-options">
                    <h4>Resolution Options:</h4>
                    <div class="resolution-buttons">
                        <button class="btn btn-outline" id="use-local" data-resolution="local-wins">
                            📱 Keep Local Changes
                        </button>
                        <button class="btn btn-outline" id="use-server" data-resolution="server-wins">
                            ☁️ Use Server Changes
                        </button>
                        <button class="btn btn-primary" id="merge-data" data-resolution="merge">
                            🔄 Merge Both (Recommended)
                        </button>
                    </div>
                </div>

                <div class="auto-resolution">
                    <label class="checkbox-label">
                        <input type="checkbox" id="auto-resolve-future">
                        <span class="checkmark"></span>
                        Automatically resolve similar conflicts in the future
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="conflict-cancel">Cancel</button>
                <button class="btn btn-primary" id="conflict-resolve" disabled>Resolve Conflict</button>
            </div>
        </div>
    </div>

    <!-- Sync Status Indicator -->
    <div class="sync-status" id="sync-status">
        <div class="sync-indicator">
            <span class="sync-icon">✓</span>
            <span class="sync-text">Synced</span>
        </div>
        <div class="conflict-badge" id="conflict-badge" style="display: none;">
            <span class="conflict-count">0</span>
            <span class="conflict-text">conflicts</span>
        </div>
    </div>

    <script>
        // Cache Service for offline functionality with conflict detection
        class CacheService {
            constructor() {
                this.prefix = 'todoapp_';
            }

            set(key, value) {
                try {
                    const versionedData = {
                        data: value,
                        version: Date.now(),
                        lastModified: new Date().toISOString(),
                        checksum: this.generateChecksum(value)
                    };
                    localStorage.setItem(this.prefix + key, JSON.stringify(versionedData));
                    console.log(`💾 Cached ${key}:`, Array.isArray(value) ? `${value.length} items` : typeof value);
                } catch (e) {
                    console.warn('Failed to save to localStorage:', e);
                }
            }

            get(key) {
                try {
                    const item = localStorage.getItem(this.prefix + key);
                    if (!item) return null;

                    const parsed = JSON.parse(item);
                    // Handle legacy data without versioning
                    if (parsed.data !== undefined) {
                        return parsed.data;
                    }
                    return parsed; // Legacy format
                } catch (e) {
                    console.warn('Failed to read from localStorage:', e);
                    return null;
                }
            }

            getVersioned(key) {
                try {
                    const item = localStorage.getItem(this.prefix + key);
                    if (!item) return null;

                    const parsed = JSON.parse(item);
                    // Handle legacy data without versioning
                    if (parsed.data === undefined) {
                        return {
                            data: parsed,
                            version: 0,
                            lastModified: new Date().toISOString(),
                            checksum: this.generateChecksum(parsed)
                        };
                    }
                    return parsed;
                } catch (e) {
                    console.warn('Failed to read versioned data from localStorage:', e);
                    return null;
                }
            }

            remove(key) {
                try {
                    localStorage.removeItem(this.prefix + key);
                } catch (e) {
                    console.warn('Failed to remove from localStorage:', e);
                }
            }

            generateChecksum(data) {
                // Simple checksum for conflict detection
                const str = JSON.stringify(data);
                let hash = 0;
                for (let i = 0; i < str.length; i++) {
                    const char = str.charCodeAt(i);
                    hash = ((hash << 5) - hash) + char;
                    hash = hash & hash; // Convert to 32-bit integer
                }
                return hash.toString();
            }

            detectConflict(key, serverData) {
                const localVersioned = this.getVersioned(key);
                if (!localVersioned) return null;

                const serverChecksum = this.generateChecksum(serverData);
                const localChecksum = localVersioned.checksum;

                if (serverChecksum !== localChecksum) {
                    return {
                        type: 'data_conflict',
                        key,
                        local: localVersioned,
                        server: {
                            data: serverData,
                            version: Date.now(),
                            lastModified: new Date().toISOString(),
                            checksum: serverChecksum
                        }
                    };
                }
                return null;
            }

            queueSync(action, id, data = null) {
                const pending = this.get('pendingSync') || [];
                pending.push({
                    action,
                    id,
                    data,
                    timestamp: Date.now(),
                    version: Date.now(),
                    retryCount: 0
                });
                this.set('pendingSync', pending);
            }

            getConflicts() {
                return this.get('conflicts') || [];
            }

            addConflict(conflict) {
                const conflicts = this.getConflicts();
                conflicts.push({
                    ...conflict,
                    id: `conflict-${Date.now()}`,
                    timestamp: Date.now(),
                    status: 'pending'
                });
                this.set('conflicts', conflicts);
            }

            resolveConflict(conflictId, resolution) {
                const conflicts = this.getConflicts();
                const conflict = conflicts.find(c => c.id === conflictId);
                if (conflict) {
                    conflict.status = 'resolved';
                    conflict.resolution = resolution;
                    conflict.resolvedAt = Date.now();
                    this.set('conflicts', conflicts);
                }
            }

            removeConflict(conflictId) {
                const conflicts = this.getConflicts();
                const filtered = conflicts.filter(c => c.id !== conflictId);
                this.set('conflicts', filtered);
            }
        }

        // Conflict Resolution Service
        class ConflictResolver {
            constructor(cache) {
                this.cache = cache;
            }

            // Automatic resolution strategies
            resolveConflictAuto(conflict, strategy = 'last-write-wins') {
                switch (strategy) {
                    case 'last-write-wins':
                        return this.lastWriteWins(conflict);
                    case 'server-wins':
                        return this.serverWins(conflict);
                    case 'local-wins':
                        return this.localWins(conflict);
                    case 'merge':
                        return this.mergeData(conflict);
                    default:
                        return this.lastWriteWins(conflict);
                }
            }

            lastWriteWins(conflict) {
                const localTime = new Date(conflict.local.lastModified).getTime();
                const serverTime = new Date(conflict.server.lastModified).getTime();

                if (serverTime > localTime) {
                    return {
                        resolution: 'server-wins',
                        data: conflict.server.data,
                        reason: 'Server data is more recent'
                    };
                } else {
                    return {
                        resolution: 'local-wins',
                        data: conflict.local.data,
                        reason: 'Local data is more recent'
                    };
                }
            }

            serverWins(conflict) {
                return {
                    resolution: 'server-wins',
                    data: conflict.server.data,
                    reason: 'Server data takes precedence'
                };
            }

            localWins(conflict) {
                return {
                    resolution: 'local-wins',
                    data: conflict.local.data,
                    reason: 'Local data takes precedence'
                };
            }

            mergeData(conflict) {
                const localData = conflict.local.data;
                const serverData = conflict.server.data;

                // Handle different data types
                if (Array.isArray(localData) && Array.isArray(serverData)) {
                    return this.mergeArrays(conflict, localData, serverData);
                } else if (typeof localData === 'object' && typeof serverData === 'object') {
                    return this.mergeObjects(conflict, localData, serverData);
                } else {
                    // For primitive types, fall back to last-write-wins
                    return this.lastWriteWins(conflict);
                }
            }

            mergeArrays(conflict, localArray, serverArray) {
                // For tasks/lists arrays, merge by ID and keep most recent versions
                const merged = [];
                const localMap = new Map();
                const serverMap = new Map();

                // Build maps for efficient lookup
                localArray.forEach(item => {
                    if (item.id) localMap.set(item.id, item);
                });
                serverArray.forEach(item => {
                    if (item.id) serverMap.set(item.id, item);
                });

                // Merge items
                const allIds = new Set([...localMap.keys(), ...serverMap.keys()]);

                allIds.forEach(id => {
                    const localItem = localMap.get(id);
                    const serverItem = serverMap.get(id);

                    if (localItem && serverItem) {
                        // Both exist, choose most recent or merge
                        const localTime = new Date(localItem.createdAt || localItem.lastModified || 0).getTime();
                        const serverTime = new Date(serverItem.createdAt || serverItem.lastModified || 0).getTime();
                        merged.push(serverTime > localTime ? serverItem : localItem);
                    } else if (localItem) {
                        merged.push(localItem);
                    } else if (serverItem) {
                        merged.push(serverItem);
                    }
                });

                return {
                    resolution: 'merged',
                    data: merged,
                    reason: 'Arrays merged by ID with most recent versions'
                };
            }

            mergeObjects(conflict, localObj, serverObj) {
                const merged = { ...localObj };

                // Merge server properties, preferring server for conflicts
                Object.keys(serverObj).forEach(key => {
                    if (key === 'lastModified' || key === 'version') {
                        // Keep server metadata
                        merged[key] = serverObj[key];
                    } else if (merged[key] !== serverObj[key]) {
                        // For conflicts, prefer server data
                        merged[key] = serverObj[key];
                    }
                });

                return {
                    resolution: 'merged',
                    data: merged,
                    reason: 'Objects merged with server data taking precedence for conflicts'
                };
            }

            // Get user-friendly conflict description
            getConflictDescription(conflict) {
                const descriptions = {
                    'tasks': 'Your tasks have been modified on another device',
                    'lists': 'Your custom lists have been modified on another device',
                    'users': 'User assignments have been modified on another device',
                    'theme': 'Theme preferences have been modified on another device',
                    'sortBy': 'Sort preferences have been modified on another device'
                };

                return descriptions[conflict.key] || `Data conflict detected for ${conflict.key}`;
            }

            // Get recommended resolution strategy
            getRecommendedStrategy(conflict) {
                const strategies = {
                    'tasks': 'merge',
                    'lists': 'merge',
                    'users': 'server-wins',
                    'theme': 'local-wins',
                    'sortBy': 'local-wins'
                };

                return strategies[conflict.key] || 'last-write-wins';
            }
        }

        // Real API Service for backend communication
        class ApiService {
            constructor() {
                this.baseUrl = 'http://localhost:5000/api';
                this.cache = new CacheService();
            }

            async request(endpoint, options = {}) {
                try {
                    const url = `${this.baseUrl}${endpoint}`;
                    const response = await fetch(url, {
                        headers: {
                            'Content-Type': 'application/json',
                            ...options.headers
                        },
                        ...options
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();
                    
                    // Handle API response format
                    if (data.success === false) {
                        throw new Error(data.message || 'API request failed');
                    }

                    return data.data || data;
                } catch (error) {
                    console.error(`API request failed: ${endpoint}`, error);
                    throw error;
                }
            }

            async getTasks() {
                return await this.request('/tasks');
            }

            async getTask(id) {
                return await this.request(`/tasks/${id}`);
            }

            async createTask(taskData) {
                return await this.request('/tasks', {
                    method: 'POST',
                    body: JSON.stringify({
                        title: taskData.title,
                        notes: taskData.notes || null,
                        dueDate: taskData.dueDate || null,
                        recurrence: taskData.recurrence || null,
                        isImportant: taskData.isImportant || false,
                        myDayDate: taskData.myDayDate || null,
                        listId: taskData.listId || null,
                        assigneeIds: taskData.assignees?.map(a => a.id) || []
                    })
                });
            }

            async updateTask(taskId, updates) {
                return await this.request(`/tasks/${taskId}`, {
                    method: 'PUT',
                    body: JSON.stringify({
                        title: updates.title,
                        notes: updates.notes,
                        isCompleted: updates.isCompleted,
                        isImportant: updates.isImportant,
                        dueDate: updates.dueDate,
                        recurrence: updates.recurrence,
                        myDayDate: updates.myDayDate,
                        listId: updates.listId,
                        assigneeIds: updates.assignees?.map(a => a.id)
                    })
                });
            }

            async deleteTask(taskId) {
                return await this.request(`/tasks/${taskId}`, {
                    method: 'DELETE'
                });
            }

            async bulkOperation(taskIds, operation) {
                return await this.request('/tasks/bulk', {
                    method: 'POST',
                    body: JSON.stringify({
                        taskIds,
                        operation
                    })
                });
            }

            async getTasksByList(listId) {
                return await this.request(`/tasks/by-list/${listId}`);
            }

            async getTasksByUser(userId) {
                return await this.request(`/tasks/by-user/${userId}`);
            }

            async getImportantTasks() {
                return await this.request('/tasks/important');
            }

            async getMyDayTasks(date) {
                return await this.request(`/tasks/my-day/${date}`);
            }

            async getPlannedTasks() {
                return await this.request('/tasks/planned');
            }

            async getLists() {
                return await this.request('/lists');
            }

            async createList(listData) {
                return await this.request('/lists', {
                    method: 'POST',
                    body: JSON.stringify({
                        name: listData.name,
                        icon: listData.icon || '📁'
                    })
                });
            }

            async updateList(listId, listData) {
                return await this.request(`/lists/${listId}`, {
                    method: 'PUT',
                    body: JSON.stringify({
                        name: listData.name,
                        icon: listData.icon
                    })
                });
            }

            async deleteList(listId) {
                return await this.request(`/lists/${listId}`, {
                    method: 'DELETE'
                });
            }

            async getUsers() {
                return await this.request('/users');
            }

            async createUser(userData) {
                return await this.request('/users', {
                    method: 'POST',
                    body: JSON.stringify(userData)
                });
            }

            async updateUser(userId, userData) {
                return await this.request(`/users/${userId}`, {
                    method: 'PUT',
                    body: JSON.stringify(userData)
                });
            }

            async deleteUser(userId) {
                return await this.request(`/users/${userId}`, {
                    method: 'DELETE'
                });
            }

            async getStats() {
                return await this.request('/stats');
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }

        // UI Manager Component
        class UIManager {
            constructor(app) {
                this.app = app;
                this.selectedTasks = new Set();
                this.bulkMode = false;
                this.currentEditTask = null;
                
                this.dom = {
                    taskInput: document.getElementById('new-task-input'),
                    taskList: document.getElementById('task-list'),
                    listTitle: document.getElementById('list-title'),
                    taskCount: document.getElementById('task-count'),
                    themeToggle: document.getElementById('theme-toggle'),
                    helpButton: document.getElementById('help-button'),
                    searchInput: document.getElementById('search-input'),
                    sortSelect: document.getElementById('sort-select'),
                    usersContainer: document.getElementById('users-container'),
                    customListsContainer: document.getElementById('custom-lists'),
                    allCount: document.getElementById('all-count'),
                    myDayCount: document.getElementById('my-day-count'),
                    importantCount: document.getElementById('important-count'),
                    plannedCount: document.getElementById('planned-count'),
                    analyticsPanel: document.getElementById('analytics-panel'),
                    // Toolbar
                    toolbar: {
                        saveBtn: document.getElementById('save-btn'),
                        loadBtn: document.getElementById('load-btn'),
                        exportCsvBtn: document.getElementById('export-csv-btn'),
                        printBtn: document.getElementById('print-btn'),
                        clearBtn: document.getElementById('clear-btn'),
                    },
                    // Modals
                    editModal: {
                        overlay: document.getElementById('edit-modal-overlay'),
                        titleInput: document.getElementById('edit-task-title'),
                        notesTextarea: document.getElementById('edit-task-notes'),
                        dueDateInput: document.getElementById('edit-task-due-date'),
                        recurrenceSelect: document.getElementById('edit-task-recurrence'),
                        assigneeList: document.getElementById('edit-assignee-list'),
                        saveBtn: document.getElementById('edit-save'),
                        cancelBtn: document.getElementById('edit-cancel'),
                        closeBtn: document.getElementById('edit-modal-close'),
                    },
                    shortcutsModal: {
                        overlay: document.getElementById('shortcuts-modal-overlay'),
                        closeBtn: document.getElementById('shortcuts-modal-close'),
                        gotItBtn: document.getElementById('shortcuts-close'),
                    },
                    conflictModal: {
                        overlay: document.getElementById('conflict-modal-overlay'),
                        closeBtn: document.getElementById('conflict-modal-close'),
                        descriptionText: document.getElementById('conflict-description-text'),
                        localData: document.getElementById('local-data'),
                        serverData: document.getElementById('server-data'),
                        useLocalBtn: document.getElementById('use-local'),
                        useServerBtn: document.getElementById('use-server'),
                        mergeBtn: document.getElementById('merge-data'),
                        autoResolveCheckbox: document.getElementById('auto-resolve-future'),
                        cancelBtn: document.getElementById('conflict-cancel'),
                        resolveBtn: document.getElementById('conflict-resolve'),
                    },
                    syncStatus: {
                        container: document.getElementById('sync-status'),
                        icon: document.querySelector('.sync-icon'),
                        text: document.querySelector('.sync-text'),
                        conflictBadge: document.getElementById('conflict-badge'),
                        conflictCount: document.querySelector('.conflict-count'),
                    },
                    // Bulk Actions
                    bulkActions: {
                        container: document.getElementById('bulk-actions'),
                        info: document.getElementById('bulk-info'),
                        completeBtn: document.getElementById('bulk-complete'),
                        deleteBtn: document.getElementById('bulk-delete'),
                        cancelBtn: document.getElementById('bulk-cancel'),
                    },
                };
            }
            
            init() {
                this.setupEventListeners();
            }
            
            render() {
                this.renderTheme();
                this.renderTasks();
                this.renderUsers();
                this.renderCustomLists();
                this.updateCounts();
                this.renderAnalytics();
                this.updateSortSelect();
            }
            
            renderTasks() {
                const { tasks } = this.app.state;
                const filteredTasks = this.app.getFilteredTasks();
                
                if (filteredTasks.length === 0) {
                    this.dom.taskList.innerHTML = this.createEmptyState();
                    return;
                }

                this.dom.taskList.innerHTML = filteredTasks
                    .map(task => this.createTaskElement(task))
                    .join('');
            }

            createEmptyState() {
                const { searchTerm } = this.app.state;
                if (searchTerm) {
                    return `
                        <div class="empty-state">
                            <div class="icon">🔍</div>
                            <h3>No results</h3>
                            <p>No tasks found for "${this.escapeHtml(searchTerm)}".</p>
                        </div>
                    `;
                }
                return `
                    <div class="empty-state">
                        <div class="icon">✅</div>
                        <h3>All clear!</h3>
                        <p>Add a new task to get started.</p>
                    </div>
                `;
            }
            
            createTaskElement(task) {
                const completedClass = task.isCompleted ? 'completed' : '';
                const importantClass = task.isImportant ? 'important' : '';
                const selectedClass = this.selectedTasks.has(task.id) ? 'selected' : '';
                const assigneesHtml = this.createAssigneesHtml(task);
                
                return `
                    <div class="task-item ${completedClass} ${importantClass} ${selectedClass}" data-task-id="${task.id}" draggable="true" tabindex="0">
                        <div class="drag-handle" title="Drag to reorder">⋮⋮</div>
                        <button class="task-checkbox" onclick="todoApp.toggleTaskCompletion('${task.id}')" aria-label="${task.isCompleted ? 'Mark as not complete' : 'Mark as complete'}">
                            ${task.isCompleted ? '<span class="checkmark">✓</span>' : ''}
                        </button>
                        <div class="task-content" onclick="todoApp.ui.openEditModalById('${task.id}')">
                            <div class="task-title">${this.escapeHtml(task.title)}</div>
                            ${task.notes ? `<div class="task-notes">${this.escapeHtml(task.notes)}</div>` : ''}
                            ${this.renderTaskMeta(task)}
                            ${assigneesHtml}
                        </div>
                        <div class="task-actions">
                            <button class="task-important task-action-btn ${task.isImportant ? 'active' : ''}"
                                    data-tooltip="${task.isImportant ? 'Remove from Important' : 'Mark as Important'}"
                                    onclick="event.stopPropagation(); todoApp.toggleTaskImportance('${task.id}')"
                                    aria-label="${task.isImportant ? 'Remove from important' : 'Mark as important'}">
                                ${task.isImportant ? '★' : '☆'}
                            </button>
                            <button class="task-duplicate task-action-btn"
                                    data-tooltip="Duplicate Task"
                                    onclick="event.stopPropagation(); todoApp.duplicateTask('${task.id}')"
                                    aria-label="Duplicate task">
                                📋
                            </button>
                            <button class="task-edit task-action-btn"
                                    data-tooltip="Edit Task"
                                    onclick="event.stopPropagation(); todoApp.ui.openEditModalById('${task.id}')"
                                    aria-label="Edit task">
                                ✏️
                            </button>
                            <button class="task-delete task-action-btn"
                                    data-tooltip="Delete Task"
                                    onclick="event.stopPropagation(); todoApp.deleteTask('${task.id}')"
                                    aria-label="Delete task">
                                🗑️
                            </button>
                        </div>
                        ${this.bulkMode ? `<input type="checkbox" class="task-select" ${this.selectedTasks.has(task.id) ? 'checked' : ''} onchange="event.stopPropagation(); todoApp.ui.toggleTaskSelection('${task.id}')" aria-label="Select task">` : ''}
                    </div>
                `;
            }

            createAssigneesHtml(task) {
                if (!task.assignees || task.assignees.length === 0) return '';

                return `
                    <div class="task-assignees">
                        ${task.assignees.map(assignee => {
                            const user = this.app.state.users.find(u => u.id === assignee.id) || assignee;
                            const initial = user.name ? user.name.trim().split(' ')[0][0].toUpperCase() : '?';
                            const color = user.avatar || '#666';
                            return `<div class="assignee-avatar" style="background-color: ${color}" title="${user.name}">
                                ${initial}
                            </div>`;
                        }).join('')}
                    </div>
                `;
            }

            renderTaskMeta(task) {
                const meta = [];

                if (task.dueDate) {
                    const dueDate = new Date(task.dueDate);
                    const isOverdue = dueDate < new Date() && !task.isCompleted;
                    meta.push(`<span class="task-due ${isOverdue ? 'overdue' : ''}" title="${dueDate.toLocaleString()}">${this.formatDate(dueDate)}</span>`);
                }

                if (task.recurrence) {
                    const recurrenceIcon = '🔄';
                    meta.push(`<span class="task-recurrence" title="Repeats ${task.recurrence}">${recurrenceIcon} ${task.recurrence}</span>`);
                }

                if (task.listId) {
                    const list = this.app.state.lists.find(l => l.id === task.listId);
                    if (list) {
                        meta.push(`<span class="task-list" title="List: ${list.name}">${list.name}</span>`);
                    }
                }

                return meta.length > 0 ? `<div class="task-meta">${meta.join(' • ')}</div>` : '';
            }

            renderUsers() {
                const { users, tasks } = this.app.state;
                if (!users || users.length === 0) return;
                
                this.dom.usersContainer.innerHTML = users.map(user => {
                    const taskCount = tasks.filter(task => 
                        !task.isCompleted && task.assignees?.some(a => a.id === user.id)
                    ).length;
                    const initial = user.name ? user.name.trim().split(' ')[0][0].toUpperCase() : '?';
                    const displayName = user.name.split(' ').map(w => w.charAt(0).toUpperCase() + w.slice(1)).join(' ');
                    
                    return `
                        <div class="user-item" data-user-id="${user.id}" role="button" tabindex="0">
                            <div class="user-avatar" style="background-color: ${user.avatar}" aria-hidden="true">
                                ${initial}
                            </div>
                            <span>${this.escapeHtml(displayName)}</span>
                            <span class="list-count">${taskCount}</span>
                        </div>
                    `;
                }).join('');
            }

            renderCustomLists() {
                const { lists, tasks } = this.app.state;
                const customLists = lists.filter(list => !list.smart);
                
                this.dom.customListsContainer.innerHTML = customLists.map(list => {
                    const taskCount = tasks.filter(t => !t.isCompleted && t.listId === list.id).length;
                    
                    return `
                        <div class="list-item" data-list-id="${list.id}" role="button" tabindex="0">
                            <span class="icon" aria-hidden="true">${list.icon || '📁'}</span>
                            <span>${this.escapeHtml(list.name)}</span>
                            <span class="list-count">${taskCount}</span>
                            <button class="list-delete task-action-btn" data-tooltip="Delete List" onclick="event.stopPropagation(); todoApp.deleteList('${list.id}')" aria-label="Delete list ${list.name}">🗑️</button>
                        </div>
                    `;
                }).join('');
            }
            
            renderTheme() {
                const { theme } = this.app.state;
                document.documentElement.setAttribute('data-theme', theme);
                this.dom.themeToggle.textContent = theme === 'light' ? '🌙' : '☀️';
            }

            updateSortSelect() {
                this.dom.sortSelect.value = this.app.state.sortBy;
            }

            focusTaskInput() {
                this.dom.taskInput.focus();
            }

            focusSearch() {
                this.dom.searchInput.focus();
            }

            showKeyboardShortcuts() {
                this.dom.shortcutsModal.overlay.style.display = 'flex';
            }

            closeKeyboardShortcuts() {
                this.dom.shortcutsModal.overlay.style.display = 'none';
            }

            showConflictResolution(conflict) {
                this.currentConflict = conflict;
                const modal = this.dom.conflictModal;

                // Set description
                modal.descriptionText.textContent = this.app.conflictResolver.getConflictDescription(conflict);

                // Show local data
                this.renderConflictData(modal.localData, conflict.local, 'Local');

                // Show server data
                this.renderConflictData(modal.serverData, conflict.server, 'Server');

                // Reset button states
                this.resetConflictButtons();

                // Show recommended strategy
                const recommended = this.app.conflictResolver.getRecommendedStrategy(conflict);
                if (recommended === 'merge') {
                    modal.mergeBtn.classList.add('selected');
                    modal.resolveBtn.disabled = false;
                    this.selectedResolution = 'merge';
                }

                modal.overlay.style.display = 'flex';
            }

            renderConflictData(container, versionedData, label) {
                const preview = container.querySelector('.data-preview');
                const timestamp = container.querySelector('.data-timestamp');

                // Format data for display
                let displayData = versionedData.data;
                if (Array.isArray(displayData)) {
                    displayData = `${displayData.length} items:\n` +
                        displayData.slice(0, 3).map(item =>
                            `• ${item.title || item.name || JSON.stringify(item).substring(0, 50)}`
                        ).join('\n') +
                        (displayData.length > 3 ? `\n... and ${displayData.length - 3} more` : '');
                } else if (typeof displayData === 'object') {
                    displayData = JSON.stringify(displayData, null, 2);
                } else {
                    displayData = String(displayData);
                }

                preview.textContent = displayData;
                timestamp.textContent = `${label} • Modified: ${new Date(versionedData.lastModified).toLocaleString()}`;
            }

            resetConflictButtons() {
                const modal = this.dom.conflictModal;
                [modal.useLocalBtn, modal.useServerBtn, modal.mergeBtn].forEach(btn => {
                    btn.classList.remove('selected');
                });
                modal.resolveBtn.disabled = true;
                this.selectedResolution = null;
            }

            selectResolution(resolution) {
                this.selectedResolution = resolution;
                this.resetConflictButtons();

                const modal = this.dom.conflictModal;
                const buttons = {
                    'local-wins': modal.useLocalBtn,
                    'server-wins': modal.useServerBtn,
                    'merge': modal.mergeBtn
                };

                if (buttons[resolution]) {
                    buttons[resolution].classList.add('selected');
                    modal.resolveBtn.disabled = false;
                }
            }

            resolveCurrentConflict() {
                if (!this.currentConflict || !this.selectedResolution) return;

                const resolution = this.app.conflictResolver.resolveConflictAuto(
                    this.currentConflict,
                    this.selectedResolution
                );

                // Apply resolution
                this.app.applyConflictResolution(this.currentConflict, resolution);

                // Handle auto-resolve preference
                if (this.dom.conflictModal.autoResolveCheckbox.checked) {
                    this.app.setAutoResolveStrategy(this.currentConflict.key, this.selectedResolution);
                }

                this.closeConflictModal();
            }

            closeConflictModal() {
                this.dom.conflictModal.overlay.style.display = 'none';
                this.currentConflict = null;
                this.selectedResolution = null;
            }

            updateSyncStatus(status, conflictCount = 0) {
                const sync = this.dom.syncStatus;

                // Update sync indicator
                sync.container.className = `sync-status ${status}`;

                switch (status) {
                    case 'syncing':
                        sync.icon.textContent = '⟳';
                        sync.text.textContent = 'Syncing...';
                        break;
                    case 'success':
                        sync.icon.textContent = '✓';
                        sync.text.textContent = 'Synced';
                        break;
                    case 'error':
                        sync.icon.textContent = '⚠';
                        sync.text.textContent = 'Sync Error';
                        break;
                    case 'offline':
                        sync.icon.textContent = '📴';
                        sync.text.textContent = 'Offline';
                        break;
                    default:
                        sync.icon.textContent = '✓';
                        sync.text.textContent = 'Synced';
                }

                // Update conflict badge
                if (conflictCount > 0) {
                    sync.conflictBadge.style.display = 'flex';
                    sync.conflictCount.textContent = conflictCount;
                } else {
                    sync.conflictBadge.style.display = 'none';
                }
            }

            renderTaskCount() {
                const count = this.app.getFilteredTasks().length;
                this.dom.taskCount.textContent = `${count} task${count !== 1 ? 's' : ''}`;
            }

            renderAnalytics() {
                const stats = this.app.getTaskStats();
                document.getElementById('stat-total').textContent = stats.total;
                document.getElementById('stat-completed').textContent = stats.completed;
                document.getElementById('stat-important').textContent = stats.important;
                document.getElementById('stat-overdue').textContent = stats.overdue;
                document.getElementById('stat-today').textContent = stats.completedToday;
            }

            updateCounts() {
                this.renderListCounts();
                this.renderTaskCount();
            }

            renderListCounts() {
                const { tasks } = this.app.state;
                const todayStr = new Date().toDateString();
                
                this.dom.allCount.textContent = tasks.filter(t => !t.isCompleted).length;
                this.dom.myDayCount.textContent = tasks.filter(t => !t.isCompleted && t.myDayDate === todayStr).length;
                this.dom.importantCount.textContent = tasks.filter(t => !t.isCompleted && t.isImportant).length;
                this.dom.plannedCount.textContent = tasks.filter(t => !t.isCompleted && t.dueDate).length;
            }

            updateListTitle() {
                const { currentList } = this.app.state;
                const titles = { all: 'All Tasks', 'my-day': 'My Day', important: 'Important', planned: 'Planned' };
                
                if (currentList.startsWith('user-')) {
                    const userId = currentList.replace('user-', '');
                    const user = this.app.state.users?.find(u => u.id === userId);
                    this.dom.listTitle.textContent = user ? `${user.name}'s Tasks` : 'User Tasks';
                } else if (titles[currentList]) {
                    this.dom.listTitle.textContent = titles[currentList];
                } else {
                    const list = this.app.state.lists?.find(l => l.id === currentList);
                    this.dom.listTitle.textContent = list ? list.name : 'All Tasks';
                }
            }
            
            escapeHtml(text) {
                if (!text) return '';
                return text.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#039;");
            }
            
            formatDate(date) {
                const today = new Date(); today.setHours(0,0,0,0);
                const tomorrow = new Date(today); tomorrow.setDate(tomorrow.getDate() + 1);
                const yesterday = new Date(today); yesterday.setDate(yesterday.getDate() - 1);
                
                const compareDate = new Date(date); compareDate.setHours(0,0,0,0);

                if (compareDate.getTime() === today.getTime()) return 'Today';
                if (compareDate.getTime() === tomorrow.getTime()) return 'Tomorrow';
                if (compareDate.getTime() === yesterday.getTime()) return 'Yesterday';
                return date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
            }
            
            openEditModalById(taskId) {
                const task = this.app.state.tasks.find(t => t.id === taskId);
                if (task) this.openEditModal(task);
            }

            openEditModal(task) {
                this.currentEditTask = task;
                const modal = this.dom.editModal;

                modal.titleInput.value = task.title || '';
                modal.notesTextarea.value = task.notes || '';
                modal.dueDateInput.value = task.dueDate || '';
                modal.recurrenceSelect.value = task.recurrence || '';

                const { users } = this.app.state;
                const assignedIds = new Set((task.assignees || []).map(a => a.id));
                modal.assigneeList.innerHTML = users.map(user => {
                    const isSelected = assignedIds.has(user.id);
                    const initial = user.name ? user.name.trim().split(' ')[0][0].toUpperCase() : '?';
                    return `
                        <div class="assignee-item ${isSelected ? 'selected' : ''}" data-user-id="${user.id}" role="option" aria-selected="${isSelected}" tabindex="0">
                            <div class="user-avatar" style="background-color: ${user.avatar}" aria-hidden="true">
                                ${initial}
                            </div>
                            <span>${this.escapeHtml(user.name)}</span>
                            <span class="checkmark">${isSelected ? '✓' : ''}</span>
                        </div>
                    `;
                }).join('');
                
                modal.overlay.classList.add('show');
                modal.titleInput.focus();
            }

            closeEditModal() {
                this.dom.editModal.overlay.classList.remove('show');
                this.currentEditTask = null;
            }

            toggleTaskSelection(taskId) {
                if (this.selectedTasks.has(taskId)) {
                    this.selectedTasks.delete(taskId);
                } else {
                    this.selectedTasks.add(taskId);
                }
                this.updateBulkActions();
                this.renderTasks(); // Re-render to show selection change
            }
            
            startBulkMode() {
                this.bulkMode = true;
                this.renderTasks();
            }

            updateBulkActions() {
                const { container, info } = this.dom.bulkActions;
                const size = this.selectedTasks.size;
                if (size > 0) {
                    container.classList.add('show');
                    info.textContent = `${size} task${size !== 1 ? 's' : ''} selected`;
                } else {
                    container.classList.remove('show');
                    this.bulkMode = false;
                }
            }
            
            cancelBulkMode() {
                this.selectedTasks.clear();
                this.bulkMode = false;
                this.renderTasks();
                this.updateBulkActions();
            }

            setActiveListItem(listId) {
                document.querySelectorAll('.list-item, .user-item').forEach(i => i.classList.remove('active'));
                const selector = listId.startsWith('user-') ? `[data-user-id="${listId.replace('user-','')}"]` : `[data-list="${listId}"], [data-list-id="${listId}"]`;
                const activeItem = document.querySelector(selector);
                if (activeItem) activeItem.classList.add('active');
            }

            setupEventListeners() {
                // Task form
                document.getElementById('task-form').addEventListener('submit', (e) => {
                    e.preventDefault();
                    const titleInput = this.dom.taskInput;
                    const dateInput = document.getElementById('new-task-date');
                    const title = titleInput.value.trim();
                    if (title) {
                        this.app.addTask({ title, dueDate: dateInput.value || undefined });
                        titleInput.value = '';
                        dateInput.value = '';
                        titleInput.focus();
                    }
                });

                // Sidebar navigation
                document.querySelector('.sidebar').addEventListener('click', e => {
                    const listItem = e.target.closest('.list-item');
                    const userItem = e.target.closest('.user-item');
                    if (listItem) {
                        const listId = listItem.dataset.list || listItem.dataset.listId;
                        this.app.switchList(listId);
                    } else if (userItem) {
                        const userId = userItem.dataset.userId;
                        this.app.switchList(`user-${userId}`);
                    }
                });

                // New list form
                document.getElementById('new-list-form').addEventListener('submit', e => {
                    e.preventDefault();
                    const input = document.getElementById('new-list-input');
                    if (input.value.trim()) {
                        this.app.addList(input.value.trim());
                        input.value = '';
                    }
                });

                // Theme toggle
                this.dom.themeToggle.addEventListener('click', () => this.app.toggleTheme());

                // Help button
                this.dom.helpButton.addEventListener('click', () => this.showKeyboardShortcuts());
                // Search
                this.dom.searchInput.addEventListener('input', e => this.app.setSearchTerm(e.target.value));

                // Sort
                this.dom.sortSelect.addEventListener('change', e => this.app.setSortBy(e.target.value));
                
                // Toolbar
                const toolbar = this.dom.toolbar;
                toolbar.saveBtn.addEventListener('click', () => this.app.saveData());
                toolbar.loadBtn.addEventListener('click', () => this.app.loadDataFromFile());
                toolbar.exportCsvBtn.addEventListener('click', () => this.app.exportCsv());
                toolbar.printBtn.addEventListener('click', () => this.app.printTasks());
                toolbar.clearBtn.addEventListener('click', () => this.app.clearCurrentTasks());

                // Modal
                const modal = this.dom.editModal;
                modal.saveBtn.addEventListener('click', () => {
                    if (!this.currentEditTask) return;
                    const title = modal.titleInput.value.trim();
                    if (!title) return;
                    const notes = modal.notesTextarea.value.trim();
                    const dueDate = modal.dueDateInput.value || undefined;
                    const recurrence = modal.recurrenceSelect.value || undefined;

                    const selectedAssigneeNodes = modal.assigneeList.querySelectorAll('.assignee-item.selected');
                    const assignees = Array.from(selectedAssigneeNodes).map(node => {
                        const userId = node.dataset.userId;
                        const user = this.app.state.users.find(u => u.id === userId);
                        return { id: user.id, name: user.name, avatar: user.avatar };
                    });

                    this.app.updateTask(this.currentEditTask.id, { title, notes, dueDate, recurrence, assignees });
                    this.closeEditModal();
                });
                modal.cancelBtn.addEventListener('click', () => this.closeEditModal());
                modal.closeBtn.addEventListener('click', () => this.closeEditModal());
                modal.overlay.addEventListener('click', e => { if (e.target === modal.overlay) this.closeEditModal() });

                // Shortcuts modal
                const shortcutsModal = this.dom.shortcutsModal;
                shortcutsModal.closeBtn.addEventListener('click', () => this.closeKeyboardShortcuts());
                shortcutsModal.gotItBtn.addEventListener('click', () => this.closeKeyboardShortcuts());
                shortcutsModal.overlay.addEventListener('click', e => { if (e.target === shortcutsModal.overlay) this.closeKeyboardShortcuts() });

                // Conflict resolution modal
                const conflictModal = this.dom.conflictModal;
                conflictModal.closeBtn.addEventListener('click', () => this.closeConflictModal());
                conflictModal.cancelBtn.addEventListener('click', () => this.closeConflictModal());
                conflictModal.resolveBtn.addEventListener('click', () => this.resolveCurrentConflict());
                conflictModal.overlay.addEventListener('click', e => { if (e.target === conflictModal.overlay) this.closeConflictModal() });

                // Resolution option buttons
                conflictModal.useLocalBtn.addEventListener('click', () => this.selectResolution('local-wins'));
                conflictModal.useServerBtn.addEventListener('click', () => this.selectResolution('server-wins'));
                conflictModal.mergeBtn.addEventListener('click', () => this.selectResolution('merge'));

                // Conflict badge click
                this.dom.syncStatus.conflictBadge.addEventListener('click', () => this.app.showNextConflict());
                
                // Custom assignee select in modal
                modal.assigneeList.addEventListener('click', e => {
                    const item = e.target.closest('.assignee-item');
                    if (!item) return;

                    item.classList.toggle('selected');
                    const isSelected = item.classList.contains('selected');
                    item.setAttribute('aria-selected', String(isSelected));
                    item.querySelector('.checkmark').textContent = isSelected ? '✓' : '';
                });

                // Bulk Actions
                const bulk = this.dom.bulkActions;
                bulk.completeBtn.addEventListener('click', () => {
                    this.selectedTasks.forEach(id => this.app.toggleTaskCompletion(id, true));
                    this.cancelBulkMode();
                });
                bulk.deleteBtn.addEventListener('click', () => {
                    if (confirm(`Delete ${this.selectedTasks.size} tasks?`)) {
                        this.selectedTasks.forEach(id => this.app.deleteTask(id, true));
                        this.cancelBulkMode();
                    }
                });
                bulk.cancelBtn.addEventListener('click', () => this.cancelBulkMode());

                // Setup drag and drop
                this.setupDragAndDrop();
            }

            setupDragAndDrop() {
                let draggedTask = null;
                let draggedOverElement = null;

                // Task list drag and drop
                this.dom.taskList.addEventListener('dragstart', (e) => {
                    const taskItem = e.target.closest('.task-item');
                    if (!taskItem) return;

                    draggedTask = taskItem.dataset.taskId;
                    taskItem.classList.add('dragging');
                    e.dataTransfer.effectAllowed = 'move';
                    e.dataTransfer.setData('text/plain', draggedTask);
                });

                this.dom.taskList.addEventListener('dragend', (e) => {
                    const taskItem = e.target.closest('.task-item');
                    if (taskItem) {
                        taskItem.classList.remove('dragging');
                    }
                    // Clean up drag over effects
                    document.querySelectorAll('.drag-over').forEach(el => el.classList.remove('drag-over'));
                    draggedTask = null;
                    draggedOverElement = null;
                });

                this.dom.taskList.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    e.dataTransfer.dropEffect = 'move';

                    const taskItem = e.target.closest('.task-item');
                    if (taskItem && taskItem.dataset.taskId !== draggedTask) {
                        // Remove previous drag-over effects
                        document.querySelectorAll('.task-item.drag-over').forEach(el => el.classList.remove('drag-over'));

                        // Add drag-over effect to current item
                        taskItem.classList.add('drag-over');
                        draggedOverElement = taskItem;
                    }
                });

                this.dom.taskList.addEventListener('drop', (e) => {
                    e.preventDefault();

                    const targetTaskItem = e.target.closest('.task-item');
                    if (targetTaskItem && draggedTask && targetTaskItem.dataset.taskId !== draggedTask) {
                        const targetTaskId = targetTaskItem.dataset.taskId;
                        this.app.reorderTasks(draggedTask, targetTaskId);
                    }

                    // Clean up
                    document.querySelectorAll('.drag-over').forEach(el => el.classList.remove('drag-over'));
                });

                // Sidebar list drag and drop
                document.querySelector('.sidebar').addEventListener('dragover', (e) => {
                    const listItem = e.target.closest('.list-item');
                    if (listItem && draggedTask) {
                        e.preventDefault();
                        e.dataTransfer.dropEffect = 'move';

                        // Remove previous drag-over effects
                        document.querySelectorAll('.list-item.drag-over').forEach(el => el.classList.remove('drag-over'));
                        listItem.classList.add('drag-over');
                    }
                });

                document.querySelector('.sidebar').addEventListener('drop', (e) => {
                    const listItem = e.target.closest('.list-item');
                    if (listItem && draggedTask) {
                        e.preventDefault();

                        const targetListId = listItem.dataset.list || listItem.dataset.listId;
                        if (targetListId) {
                            this.app.moveTaskToList(draggedTask, targetListId);
                        }
                    }

                    // Clean up
                    document.querySelectorAll('.list-item.drag-over').forEach(el => el.classList.remove('drag-over'));
                });
            }
        }

        // Initialize the app when DOM is loaded
        let todoApp;
        document.addEventListener('DOMContentLoaded', () => {
            todoApp = new TodoApp();
            window.todoApp = todoApp; // Make it globally accessible for onclick handlers
        });
    </script>
</body>
</html>