{"GlobalPropertiesHash": "d2tfBgIL7hkkqlF4wAGkC9QaEVkKhkl70t1B+H45M6w=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["20PefdQbv3csmFJ6+wuVPu0bo0n7u2xrEKMHBEOvrcI=", "Nm0yz8wEU4RHs/muXgRqjXcQ99yUjGOeNQJUXSax9/o=", "A86GPILaH+s5dLlkTpXLMGLptiiVWp34MORuBpk+qyQ=", "WYWpx5OpjKiQBfp461Yp4Q0/hBYOhBDse9kM220QIbk="], "CachedAssets": {"20PefdQbv3csmFJ6+wuVPu0bo0n7u2xrEKMHBEOvrcI=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index.html", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5lbvunm6pn", "Integrity": "lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 1941, "LastWriteTime": "2025-09-28T03:04:20+00:00"}, "Nm0yz8wEU4RHs/muXgRqjXcQ99yUjGOeNQJUXSax9/o=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9.html", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "index9#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "16oku2ib4b", "Integrity": "p0KvelnDSwL8iAS6MAwBF6Oxcmf79vfFndKrWXfqEuk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index9.html", "FileLength": 118816, "LastWriteTime": "2025-09-28T12:34:02+00:00"}}, "CachedCopyCandidates": {}}