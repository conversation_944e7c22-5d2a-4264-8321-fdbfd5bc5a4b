{"GlobalPropertiesHash": "d2tfBgIL7hkkqlF4wAGkC9QaEVkKhkl70t1B+H45M6w=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["yzIcQMOVGHnyzstmlvj36oozurdN0dI3V+SJ1KJINLg=", "s6N4oSzOmM4gmhU9PvaYIbJjcMe6lgCInHRhnnSwPPM=", "aaiVCOJbM99GTghyKV7Pc05Hk6maZGBoFVXjHdB03J8=", "ET6Tgpu59U7TYVSyCNNPylrb4FwHPNgAzKv0im0Dpkw=", "P6ZS+OmE0oTcJQqlOK27E9oZEdskT4MgaBRGdGCAMlk=", "/yaahEohO3abOyRQI9iQelACTClpVxL9UAYTYosoUAw=", "c3mL7ua8irXBaFqKFcXVkUUC+IWZ+vJ/x/r8xB6vNkg=", "Sl0Gt3/u6d2mucSmcO8zQFe26TmUXhnGl6B+wVqANds=", "f9XAQHo0HNTkz7QKoIpBgk5wwx7UCluysdgVmdwzQlE=", "A86GPILaH+s5dLlkTpXLMGLptiiVWp34MORuBpk+qyQ=", "WYWpx5OpjKiQBfp461Yp4Q0/hBYOhBDse9kM220QIbk="], "CachedAssets": {"Sl0Gt3/u6d2mucSmcO8zQFe26TmUXhnGl6B+wVqANds=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\todo-app.js", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "todo-app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "yirdgu4cai", "Integrity": "mlSsPrANl7IrzlrokoEDKJSmbLi4QBbcYdmBQzgO/OE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\todo-app.js", "FileLength": 20502, "LastWriteTime": "2025-09-28T13:46:38+00:00"}, "c3mL7ua8irXBaFqKFcXVkUUC+IWZ+vJ/x/r8xB6vNkg=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\styles.css", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "styles#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mb95wunvkt", "Integrity": "jrlspTk8eSbhsrt52DJGB/Nz9GniPonpBzfSfcVbagM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\styles.css", "FileLength": 26007, "LastWriteTime": "2025-09-28T13:43:06+00:00"}, "aaiVCOJbM99GTghyKV7Pc05Hk6maZGBoFVXjHdB03J8=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index9 - Copy.html", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "backup/index9 - Copy#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "uk5yjv168n", "Integrity": "vq9BTSuUyPJUcsH6XlQUs9F9kV3CaujKXxugiI9tTho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backup\\index9 - Copy.html", "FileLength": 133787, "LastWriteTime": "2025-09-28T13:11:54+00:00"}, "s6N4oSzOmM4gmhU9PvaYIbJjcMe6lgCInHRhnnSwPPM=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index.html", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "backup/index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5lbvunm6pn", "Integrity": "lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backup\\index.html", "FileLength": 1941, "LastWriteTime": "2025-09-28T03:04:20+00:00"}, "yzIcQMOVGHnyzstmlvj36oozurdN0dI3V+SJ1KJINLg=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\app.js", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lntny8cd3c", "Integrity": "H4gxE1xMACmg7wuf/flp2mMTGKwfv5mwKRRaTBoCjRY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.js", "FileLength": 16435, "LastWriteTime": "2025-09-28T13:46:38+00:00"}, "ET6Tgpu59U7TYVSyCNNPylrb4FwHPNgAzKv0im0Dpkw=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9-backup.html", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "index9-backup#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "595uqhdyu2", "Integrity": "GQRjVfF7pbEugsQ7SDV9U/FuUPuY5ZOD9tVJiNerGB8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index9-backup.html", "FileLength": 100054, "LastWriteTime": "2025-09-28T13:24:04+00:00"}, "P6ZS+OmE0oTcJQqlOK27E9oZEdskT4MgaBRGdGCAMlk=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9-clean.html", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "index9-clean#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q7oz31h1by", "Integrity": "qsNdnqEp0inrPmO8N/ZnGqNgSDA61ZyrmjWpC/at1QU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index9-clean.html", "FileLength": 19835, "LastWriteTime": "2025-09-28T13:43:06+00:00"}, "/yaahEohO3abOyRQI9iQelACTClpVxL9UAYTYosoUAw=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9.html", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "index9#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "olqgrn1tj4", "Integrity": "VA5sqO/RUbif/XskndwzuQNSH2ux7gWVWisbWAmTSjU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index9.html", "FileLength": 19419, "LastWriteTime": "2025-09-28T13:47:18+00:00"}, "f9XAQHo0HNTkz7QKoIpBgk5wwx7UCluysdgVmdwzQlE=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\ui-manager.js", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "ui-manager#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zhln9tevi0", "Integrity": "aVY1Nq6bMgvQ71moBOwjdYBcuWJcjIAAt1g4+XIsTFE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\ui-manager.js", "FileLength": 35289, "LastWriteTime": "2025-09-28T13:46:38+00:00"}}, "CachedCopyCandidates": {}}