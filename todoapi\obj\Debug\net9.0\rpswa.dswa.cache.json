{"GlobalPropertiesHash": "d2tfBgIL7hkkqlF4wAGkC9QaEVkKhkl70t1B+H45M6w=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["fQootFUkpEixEfgi3H79Om1DIrahmUbqyyQa6UN1TYA=", "ky9gSCVl5y5R0ugBYyqzEi3tj48cWVbcVtHcVKCScAY=", "A86GPILaH+s5dLlkTpXLMGLptiiVWp34MORuBpk+qyQ=", "WYWpx5OpjKiQBfp461Yp4Q0/hBYOhBDse9kM220QIbk="], "CachedAssets": {"ky9gSCVl5y5R0ugBYyqzEi3tj48cWVbcVtHcVKCScAY=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9.html", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "index9#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o7kzmwadhj", "Integrity": "FIde/dU7aQUKQtdbytzJ080hCNDsB92jY1QRTZx4xU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index9.html", "FileLength": 118795, "LastWriteTime": "2025-09-28T04:14:16+00:00"}, "fQootFUkpEixEfgi3H79Om1DIrahmUbqyyQa6UN1TYA=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index.html", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5lbvunm6pn", "Integrity": "lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 1941, "LastWriteTime": "2025-09-28T03:04:20+00:00"}}, "CachedCopyCandidates": {}}