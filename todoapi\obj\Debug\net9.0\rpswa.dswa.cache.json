{"GlobalPropertiesHash": "d2tfBgIL7hkkqlF4wAGkC9QaEVkKhkl70t1B+H45M6w=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["ojsEjogTCcHFn8UbdEXnFxEgUiNLMqWJ3GOZuoqblHQ=", "GPumtN6IPoCdDdVUJG7SDddPWrw2GI/tE7ayV3QPM8c=", "HuSwSF6aYlEbaKa6laOa1nnHJMTc2bvM0wSUmDMi/mk=", "ZRNoH8EuMTS7lQBGtpfD4yS4fCuAnK6QLs79ueDcmaM=", "lNl2hvMomtfLNFpPVMuAhqGNSDGrUnnxxwlVsPssyUI=", "Jd3bAHG4o9vDEiRCECbQYJn7kOf08n9KOxFXEJ0Jm8o=", "a40qqWDUiXLTL539oQPWULfPQqvdi4CjBGe2PQVlfxc=", "Oa+b7h+CkfX7jvE0fllU6bfoTTi+yk4CKAJlymi0KVs=", "leNlHaNvW+3hRJb9dTHQn1rThPR09zJTsdduZ86YMfs=", "6/GfB6vXUtP6fJjeMuG6Ktk+lwxlHXs7ZA2HI8dAnqo=", "Zq6kv4OqfWlu4E80bu1J2CmbJLnRFIAQ1T4u1MKXvdc=", "bSKYWMs6jBrazgPwKk3e5JxtIR6nAnnC4hKbVog/jH4=", "AB4qbqhNGE8KvBO2eK78mEWUJCjVxeDjoQr8qpjxMxU=", "HlhOI3JeBE2lr3Wu/uOTg1xWFLdDA47wiA3qkRs+41U=", "A86GPILaH+s5dLlkTpXLMGLptiiVWp34MORuBpk+qyQ=", "WYWpx5OpjKiQBfp461Yp4Q0/hBYOhBDse9kM220QIbk="], "CachedAssets": {"GPumtN6IPoCdDdVUJG7SDddPWrw2GI/tE7ayV3QPM8c=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index.html", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "backup/index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5lbvunm6pn", "Integrity": "lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backup\\index.html", "FileLength": 1941, "LastWriteTime": "2025-09-28T03:04:20+00:00"}, "HuSwSF6aYlEbaKa6laOa1nnHJMTc2bvM0wSUmDMi/mk=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index9 - Copy.html", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "backup/index9 - Copy#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "uk5yjv168n", "Integrity": "vq9BTSuUyPJUcsH6XlQUs9F9kV3CaujKXxugiI9tTho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backup\\index9 - Copy.html", "FileLength": 133787, "LastWriteTime": "2025-09-28T13:11:54+00:00"}, "ZRNoH8EuMTS7lQBGtpfD4yS4fCuAnK6QLs79ueDcmaM=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index9-backup.html", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "backup/index9-backup#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "595uqhdyu2", "Integrity": "GQRjVfF7pbEugsQ7SDV9U/FuUPuY5ZOD9tVJiNerGB8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backup\\index9-backup.html", "FileLength": 100054, "LastWriteTime": "2025-09-28T13:24:04+00:00"}, "Zq6kv4OqfWlu4E80bu1J2CmbJLnRFIAQ1T4u1MKXvdc=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\simple-ui.js", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "simple-ui#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5y8ega3ek8", "Integrity": "zUznKZzmO64GckmbAPqKkQFm1iVXSw+vfiaMIDuGHec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\simple-ui.js", "FileLength": 43166, "LastWriteTime": "2025-09-29T00:18:30+00:00"}, "bSKYWMs6jBrazgPwKk3e5JxtIR6nAnnC4hKbVog/jH4=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\styles.css", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "styles#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mb95wunvkt", "Integrity": "jrlspTk8eSbhsrt52DJGB/Nz9GniPonpBzfSfcVbagM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\styles.css", "FileLength": 26007, "LastWriteTime": "2025-09-28T13:43:06+00:00"}, "lNl2hvMomtfLNFpPVMuAhqGNSDGrUnnxxwlVsPssyUI=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index9-clean.html", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "backup/index9-clean#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q7oz31h1by", "Integrity": "qsNdnqEp0inrPmO8N/ZnGqNgSDA61ZyrmjWpC/at1QU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backup\\index9-clean.html", "FileLength": 19835, "LastWriteTime": "2025-09-28T13:43:06+00:00"}, "Oa+b7h+CkfX7jvE0fllU6bfoTTi+yk4CKAJlymi0KVs=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index10.html", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "index10#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5lfc2i8sl7", "Integrity": "F9zrKUdWGGfzx3oZExo1GckqOSWq8RRD0//i3xJ7vP0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index10.html", "FileLength": 16291, "LastWriteTime": "2025-09-28T23:42:30+00:00"}, "Jd3bAHG4o9vDEiRCECbQYJn7kOf08n9KOxFXEJ0Jm8o=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index9.html", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "backup/index9#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "olqgrn1tj4", "Integrity": "VA5sqO/RUbif/XskndwzuQNSH2ux7gWVWisbWAmTSjU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backup\\index9.html", "FileLength": 19419, "LastWriteTime": "2025-09-28T13:47:18+00:00"}, "ojsEjogTCcHFn8UbdEXnFxEgUiNLMqWJ3GOZuoqblHQ=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\app.js", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lntny8cd3c", "Integrity": "H4gxE1xMACmg7wuf/flp2mMTGKwfv5mwKRRaTBoCjRY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.js", "FileLength": 16435, "LastWriteTime": "2025-09-28T13:46:38+00:00"}, "AB4qbqhNGE8KvBO2eK78mEWUJCjVxeDjoQr8qpjxMxU=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\todo-app.js", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "todo-app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "yirdgu4cai", "Integrity": "mlSsPrANl7IrzlrokoEDKJSmbLi4QBbcYdmBQzgO/OE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\todo-app.js", "FileLength": 20502, "LastWriteTime": "2025-09-28T13:46:38+00:00"}, "leNlHaNvW+3hRJb9dTHQn1rThPR09zJTsdduZ86YMfs=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\seed-data.js", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "seed-data#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d3jph5ando", "Integrity": "F46Je7HI8wnIscyex3c123RhF/D2eVWaKrx5XIWBPio=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\seed-data.js", "FileLength": 10805, "LastWriteTime": "2025-09-29T00:24:28+00:00"}, "6/GfB6vXUtP6fJjeMuG6Ktk+lwxlHXs7ZA2HI8dAnqo=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\simple-app.js", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "simple-app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9uz0c0wwgo", "Integrity": "cUHucJIsTB4EkA04v8uAfNCIjm8GCtdAppp+oBnHhdM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\simple-app.js", "FileLength": 14336, "LastWriteTime": "2025-09-29T00:15:12+00:00"}, "HlhOI3JeBE2lr3Wu/uOTg1xWFLdDA47wiA3qkRs+41U=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\ui-manager.js", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "ui-manager#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zhln9tevi0", "Integrity": "aVY1Nq6bMgvQ71moBOwjdYBcuWJcjIAAt1g4+XIsTFE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\ui-manager.js", "FileLength": 35289, "LastWriteTime": "2025-09-28T13:46:38+00:00"}, "a40qqWDUiXLTL539oQPWULfPQqvdi4CjBGe2PQVlfxc=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\clear-and-seed.html", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "clear-and-seed#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fdz2vga55j", "Integrity": "k741n5Z+JXFj7yYLlHeFy+j/7UT/1dykoClf/AxiMZQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\clear-and-seed.html", "FileLength": 11657, "LastWriteTime": "2025-09-29T00:25:52+00:00"}}, "CachedCopyCandidates": {}}