namespace todoapi.DTOs;

public class TodoTaskDto
{
    public string Id { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string? Notes { get; set; }
    public bool IsCompleted { get; set; }
    public bool IsImportant { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public DateTime? DueDate { get; set; }
    public string? Recurrence { get; set; }
    public string? MyDayDate { get; set; }
    public string? ListId { get; set; }
    public List<AssigneeDto> Assignees { get; set; } = new();
}

public class CreateTodoTaskDto
{
    public string Title { get; set; } = string.Empty;
    public string? Notes { get; set; }
    public DateTime? DueDate { get; set; }
    public string? Recurrence { get; set; }
    public bool IsImportant { get; set; } = false;
    public string? MyDayDate { get; set; }
    public string? ListId { get; set; }
    public List<string> AssigneeIds { get; set; } = new();
}

public class UpdateTodoTaskDto
{
    public string? Title { get; set; }
    public string? Notes { get; set; }
    public bool? IsCompleted { get; set; }
    public bool? IsImportant { get; set; }
    public DateTime? DueDate { get; set; }
    public string? Recurrence { get; set; }
    public string? MyDayDate { get; set; }
    public string? ListId { get; set; }
    public List<string>? AssigneeIds { get; set; }
}

public class TodoListDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Icon { get; set; }
    public bool Smart { get; set; }
    public DateTime CreatedAt { get; set; }
    public int TaskCount { get; set; }
}

public class CreateTodoListDto
{
    public string Name { get; set; } = string.Empty;
    public string? Icon { get; set; } = "📁";
}

public class UserDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Avatar { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public int TaskCount { get; set; }
}

public class CreateUserDto
{
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Avatar { get; set; } = "#4CAF50";
}

public class AssigneeDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Avatar { get; set; } = string.Empty;
}

public class ApiResponse<T>
{
    public bool Success { get; set; } = true;
    public string Message { get; set; } = string.Empty;
    public T? Data { get; set; }
    public List<string> Errors { get; set; } = new();
}

public class BulkOperationDto
{
    public List<string> TaskIds { get; set; } = new();
    public string Operation { get; set; } = string.Empty; // "complete", "delete", "important"
}