using Microsoft.AspNetCore.Mvc;
using todoapi.DTOs;
using todoapi.Services;

namespace todoapi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class TasksController : ControllerBase
{
    private readonly ITodoService _todoService;
    private readonly ILogger<TasksController> _logger;

    public TasksController(ITodoService todoService, ILogger<TasksController> logger)
    {
        _todoService = todoService;
        _logger = logger;
    }

    /// <summary>
    /// Get all tasks
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<IEnumerable<TodoTaskDto>>>> GetAllTasks()
    {
        try
        {
            var tasks = await _todoService.GetAllTasksAsync();
            return Ok(new ApiResponse<IEnumerable<TodoTaskDto>>
            {
                Success = true,
                Data = tasks,
                Message = "Tasks retrieved successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all tasks");
            return StatusCode(500, new ApiResponse<IEnumerable<TodoTaskDto>>
            {
                Success = false,
                Message = "Internal server error",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Get task by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<ApiResponse<TodoTaskDto>>> GetTask(string id)
    {
        try
        {
            var task = await _todoService.GetTaskByIdAsync(id);
            if (task == null)
            {
                return NotFound(new ApiResponse<TodoTaskDto>
                {
                    Success = false,
                    Message = "Task not found"
                });
            }

            return Ok(new ApiResponse<TodoTaskDto>
            {
                Success = true,
                Data = task,
                Message = "Task retrieved successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting task {TaskId}", id);
            return StatusCode(500, new ApiResponse<TodoTaskDto>
            {
                Success = false,
                Message = "Internal server error",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Create a new task
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<ApiResponse<TodoTaskDto>>> CreateTask([FromBody] CreateTodoTaskDto taskDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new ApiResponse<TodoTaskDto>
                {
                    Success = false,
                    Message = "Validation failed",
                    Errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList()
                });
            }

            var createdTask = await _todoService.CreateTaskAsync(taskDto);
            return CreatedAtAction(nameof(GetTask), new { id = createdTask.Id }, 
                new ApiResponse<TodoTaskDto>
                {
                    Success = true,
                    Data = createdTask,
                    Message = "Task created successfully"
                });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating task");
            return StatusCode(500, new ApiResponse<TodoTaskDto>
            {
                Success = false,
                Message = "Internal server error",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Update an existing task
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<ApiResponse<TodoTaskDto>>> UpdateTask(string id, [FromBody] UpdateTodoTaskDto taskDto)
    {
        try
        {
            var updatedTask = await _todoService.UpdateTaskAsync(id, taskDto);
            if (updatedTask == null)
            {
                return NotFound(new ApiResponse<TodoTaskDto>
                {
                    Success = false,
                    Message = "Task not found"
                });
            }

            return Ok(new ApiResponse<TodoTaskDto>
            {
                Success = true,
                Data = updatedTask,
                Message = "Task updated successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating task {TaskId}", id);
            return StatusCode(500, new ApiResponse<TodoTaskDto>
            {
                Success = false,
                Message = "Internal server error",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Delete a task
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult<ApiResponse<bool>>> DeleteTask(string id)
    {
        try
        {
            var result = await _todoService.DeleteTaskAsync(id);
            if (!result)
            {
                return NotFound(new ApiResponse<bool>
                {
                    Success = false,
                    Message = "Task not found"
                });
            }

            return Ok(new ApiResponse<bool>
            {
                Success = true,
                Data = true,
                Message = "Task deleted successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting task {TaskId}", id);
            return StatusCode(500, new ApiResponse<bool>
            {
                Success = false,
                Message = "Internal server error",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Bulk operations on tasks
    /// </summary>
    [HttpPost("bulk")]
    public async Task<ActionResult<ApiResponse<bool>>> BulkOperation([FromBody] BulkOperationDto bulkDto)
    {
        try
        {
            var result = await _todoService.BulkOperationAsync(bulkDto);
            return Ok(new ApiResponse<bool>
            {
                Success = result,
                Data = result,
                Message = result ? "Bulk operation completed successfully" : "Bulk operation failed"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing bulk operation");
            return StatusCode(500, new ApiResponse<bool>
            {
                Success = false,
                Message = "Internal server error",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Get tasks by list
    /// </summary>
    [HttpGet("by-list/{listId}")]
    public async Task<ActionResult<ApiResponse<IEnumerable<TodoTaskDto>>>> GetTasksByList(string listId)
    {
        try
        {
            var tasks = await _todoService.GetTasksByListAsync(listId);
            return Ok(new ApiResponse<IEnumerable<TodoTaskDto>>
            {
                Success = true,
                Data = tasks,
                Message = "Tasks retrieved successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tasks by list {ListId}", listId);
            return StatusCode(500, new ApiResponse<IEnumerable<TodoTaskDto>>
            {
                Success = false,
                Message = "Internal server error",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Get tasks by user
    /// </summary>
    [HttpGet("by-user/{userId}")]
    public async Task<ActionResult<ApiResponse<IEnumerable<TodoTaskDto>>>> GetTasksByUser(string userId)
    {
        try
        {
            var tasks = await _todoService.GetTasksByUserAsync(userId);
            return Ok(new ApiResponse<IEnumerable<TodoTaskDto>>
            {
                Success = true,
                Data = tasks,
                Message = "Tasks retrieved successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tasks by user {UserId}", userId);
            return StatusCode(500, new ApiResponse<IEnumerable<TodoTaskDto>>
            {
                Success = false,
                Message = "Internal server error",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Get important tasks
    /// </summary>
    [HttpGet("important")]
    public async Task<ActionResult<ApiResponse<IEnumerable<TodoTaskDto>>>> GetImportantTasks()
    {
        try
        {
            var tasks = await _todoService.GetImportantTasksAsync();
            return Ok(new ApiResponse<IEnumerable<TodoTaskDto>>
            {
                Success = true,
                Data = tasks,
                Message = "Important tasks retrieved successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting important tasks");
            return StatusCode(500, new ApiResponse<IEnumerable<TodoTaskDto>>
            {
                Success = false,
                Message = "Internal server error",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Get my day tasks
    /// </summary>
    [HttpGet("my-day/{date}")]
    public async Task<ActionResult<ApiResponse<IEnumerable<TodoTaskDto>>>> GetMyDayTasks(string date)
    {
        try
        {
            var tasks = await _todoService.GetMyDayTasksAsync(date);
            return Ok(new ApiResponse<IEnumerable<TodoTaskDto>>
            {
                Success = true,
                Data = tasks,
                Message = "My day tasks retrieved successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting my day tasks for {Date}", date);
            return StatusCode(500, new ApiResponse<IEnumerable<TodoTaskDto>>
            {
                Success = false,
                Message = "Internal server error",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Get planned tasks (with due dates)
    /// </summary>
    [HttpGet("planned")]
    public async Task<ActionResult<ApiResponse<IEnumerable<TodoTaskDto>>>> GetPlannedTasks()
    {
        try
        {
            var tasks = await _todoService.GetPlannedTasksAsync();
            return Ok(new ApiResponse<IEnumerable<TodoTaskDto>>
            {
                Success = true,
                Data = tasks,
                Message = "Planned tasks retrieved successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting planned tasks");
            return StatusCode(500, new ApiResponse<IEnumerable<TodoTaskDto>>
            {
                Success = false,
                Message = "Internal server error",
                Errors = { ex.Message }
            });
        }
    }
}