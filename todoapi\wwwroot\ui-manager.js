// UI Manager Component
class UIManager {
    constructor(app) {
        this.app = app;
        this.selectedTasks = new Set();
        this.bulkMode = false;
        this.currentEditTask = null;
        
        this.dom = {
            taskInput: document.getElementById('new-task-input'),
            taskList: document.getElementById('task-list'),
            listTitle: document.getElementById('list-title'),
            taskCount: document.getElementById('task-count'),
            themeToggle: document.getElementById('theme-toggle'),
            helpButton: document.getElementById('help-button'),
            searchInput: document.getElementById('search-input'),
            sortSelect: document.getElementById('sort-select'),
            usersContainer: document.getElementById('users-container'),
            customListsContainer: document.getElementById('custom-lists'),
            allCount: document.getElementById('all-count'),
            myDayCount: document.getElementById('my-day-count'),
            importantCount: document.getElementById('important-count'),
            plannedCount: document.getElementById('planned-count'),
            analyticsPanel: document.getElementById('analytics-panel'),
            // Toolbar
            toolbar: {
                saveBtn: document.getElementById('save-btn'),
                loadBtn: document.getElementById('load-btn'),
                exportCsvBtn: document.getElementById('export-csv-btn'),
                printBtn: document.getElementById('print-btn'),
                clearBtn: document.getElementById('clear-btn'),
            },
            // Modals
            editModal: {
                overlay: document.getElementById('edit-modal-overlay'),
                titleInput: document.getElementById('edit-task-title'),
                notesTextarea: document.getElementById('edit-task-notes'),
                dueDateInput: document.getElementById('edit-task-due-date'),
                recurrenceSelect: document.getElementById('edit-task-recurrence'),
                assigneeList: document.getElementById('edit-assignee-list'),
                saveBtn: document.getElementById('edit-save'),
                cancelBtn: document.getElementById('edit-cancel'),
                closeBtn: document.getElementById('edit-modal-close'),
            },
            shortcutsModal: {
                overlay: document.getElementById('shortcuts-modal-overlay'),
                closeBtn: document.getElementById('shortcuts-modal-close'),
                gotItBtn: document.getElementById('shortcuts-close'),
            },
            conflictModal: {
                overlay: document.getElementById('conflict-modal-overlay'),
                closeBtn: document.getElementById('conflict-modal-close'),
                descriptionText: document.getElementById('conflict-description-text'),
                localData: document.getElementById('local-data'),
                serverData: document.getElementById('server-data'),
                useLocalBtn: document.getElementById('use-local'),
                useServerBtn: document.getElementById('use-server'),
                mergeBtn: document.getElementById('merge-data'),
                autoResolveCheckbox: document.getElementById('auto-resolve-future'),
                cancelBtn: document.getElementById('conflict-cancel'),
                resolveBtn: document.getElementById('conflict-resolve'),
            },
            syncStatus: {
                container: document.getElementById('sync-status'),
                icon: document.querySelector('.sync-icon'),
                text: document.querySelector('.sync-text'),
                conflictBadge: document.getElementById('conflict-badge'),
                conflictCount: document.querySelector('.conflict-count'),
            },
            // Bulk Actions
            bulkActions: {
                container: document.getElementById('bulk-actions'),
                info: document.getElementById('bulk-info'),
                completeBtn: document.getElementById('bulk-complete'),
                deleteBtn: document.getElementById('bulk-delete'),
                cancelBtn: document.getElementById('bulk-cancel'),
            },
        };
    }
    
    init() {
        this.setupEventListeners();
    }
    
    render() {
        this.renderTheme();
        this.renderTasks();
        this.renderUsers();
        this.renderCustomLists();
        this.updateCounts();
        this.renderAnalytics();
        this.updateSortSelect();
    }
    
    renderTasks() {
        const { tasks } = this.app.state;
        const filteredTasks = this.app.getFilteredTasks();
        
        if (filteredTasks.length === 0) {
            this.dom.taskList.innerHTML = this.createEmptyState();
            return;
        }

        this.dom.taskList.innerHTML = filteredTasks
            .map(task => this.createTaskElement(task))
            .join('');
    }

    createEmptyState() {
        const { searchTerm } = this.app.state;
        if (searchTerm) {
            return `
                <div class="empty-state">
                    <div class="icon">🔍</div>
                    <h3>No results</h3>
                    <p>No tasks found for "${this.escapeHtml(searchTerm)}".</p>
                </div>
            `;
        }
        return `
            <div class="empty-state">
                <div class="icon">✅</div>
                <h3>All clear!</h3>
                <p>Add a new task to get started.</p>
            </div>
        `;
    }
    
    createTaskElement(task) {
        const completedClass = task.isCompleted ? 'completed' : '';
        const importantClass = task.isImportant ? 'important' : '';
        const selectedClass = this.selectedTasks.has(task.id) ? 'selected' : '';
        const assigneesHtml = this.createAssigneesHtml(task);
        
        return `
            <div class="task-item ${completedClass} ${importantClass} ${selectedClass}" data-task-id="${task.id}" draggable="true" tabindex="0">
                <div class="drag-handle" title="Drag to reorder">⋮⋮</div>
                <button class="task-checkbox" onclick="todoApp.toggleTaskCompletion('${task.id}')" aria-label="${task.isCompleted ? 'Mark as not complete' : 'Mark as complete'}">
                    ${task.isCompleted ? '<span class="checkmark">✓</span>' : ''}
                </button>
                <div class="task-content" onclick="todoApp.ui.openEditModalById('${task.id}')">
                    <div class="task-title">${this.escapeHtml(task.title)}</div>
                    ${task.notes ? `<div class="task-notes">${this.escapeHtml(task.notes)}</div>` : ''}
                    ${this.renderTaskMeta(task)}
                    ${assigneesHtml}
                </div>
                <div class="task-actions">
                    <button class="task-important task-action-btn ${task.isImportant ? 'active' : ''}"
                            data-tooltip="${task.isImportant ? 'Remove from Important' : 'Mark as Important'}"
                            onclick="event.stopPropagation(); todoApp.toggleTaskImportance('${task.id}')"
                            aria-label="${task.isImportant ? 'Remove from important' : 'Mark as important'}">
                        ${task.isImportant ? '★' : '☆'}
                    </button>
                    <button class="task-duplicate task-action-btn"
                            data-tooltip="Duplicate Task"
                            onclick="event.stopPropagation(); todoApp.duplicateTask('${task.id}')"
                            aria-label="Duplicate task">
                        📋
                    </button>
                    <button class="task-edit task-action-btn"
                            data-tooltip="Edit Task"
                            onclick="event.stopPropagation(); todoApp.ui.openEditModalById('${task.id}')"
                            aria-label="Edit task">
                        ✏️
                    </button>
                    <button class="task-delete task-action-btn"
                            data-tooltip="Delete Task"
                            onclick="event.stopPropagation(); todoApp.deleteTask('${task.id}')"
                            aria-label="Delete task">
                        🗑️
                    </button>
                </div>
                ${this.bulkMode ? `<input type="checkbox" class="task-select" ${this.selectedTasks.has(task.id) ? 'checked' : ''} onchange="event.stopPropagation(); todoApp.ui.toggleTaskSelection('${task.id}')" aria-label="Select task">` : ''}
            </div>
        `;
    }

    createAssigneesHtml(task) {
        if (!task.assignees || task.assignees.length === 0) return '';

        return `
            <div class="task-assignees">
                ${task.assignees.map(assignee => {
                    const user = this.app.state.users.find(u => u.id === assignee.id) || assignee;
                    const initial = user.name ? user.name.trim().split(' ')[0][0].toUpperCase() : '?';
                    const color = user.avatar || '#666';
                    return `<div class="assignee-avatar" style="background-color: ${color}" title="${user.name}">
                        ${initial}
                    </div>`;
                }).join('')}
            </div>
        `;
    }

    renderTaskMeta(task) {
        const meta = [];

        if (task.dueDate) {
            const dueDate = new Date(task.dueDate);
            const isOverdue = dueDate < new Date() && !task.isCompleted;
            meta.push(`<span class="task-due ${isOverdue ? 'overdue' : ''}" title="${dueDate.toLocaleString()}">${this.formatDate(dueDate)}</span>`);
        }

        if (task.recurrence) {
            const recurrenceIcon = '🔄';
            meta.push(`<span class="task-recurrence" title="Repeats ${task.recurrence}">${recurrenceIcon} ${task.recurrence}</span>`);
        }

        if (task.listId) {
            const list = this.app.state.lists.find(l => l.id === task.listId);
            if (list) {
                meta.push(`<span class="task-list" title="List: ${list.name}">${list.name}</span>`);
            }
        }

        return meta.length > 0 ? `<div class="task-meta">${meta.join(' • ')}</div>` : '';
    }

    renderUsers() {
        const { users, tasks } = this.app.state;
        if (!users || users.length === 0) return;
        
        this.dom.usersContainer.innerHTML = users.map(user => {
            const taskCount = tasks.filter(task => 
                !task.isCompleted && task.assignees?.some(a => a.id === user.id)
            ).length;
            const initial = user.name ? user.name.trim().split(' ')[0][0].toUpperCase() : '?';
            const displayName = user.name.split(' ').map(w => w.charAt(0).toUpperCase() + w.slice(1)).join(' ');
            
            return `
                <div class="user-item" data-user-id="${user.id}" role="button" tabindex="0">
                    <div class="user-avatar" style="background-color: ${user.avatar}" aria-hidden="true">
                        ${initial}
                    </div>
                    <span>${this.escapeHtml(displayName)}</span>
                    <span class="list-count">${taskCount}</span>
                </div>
            `;
        }).join('');
    }

    renderCustomLists() {
        const { lists, tasks } = this.app.state;
        const customLists = lists.filter(list => !list.smart);
        
        this.dom.customListsContainer.innerHTML = customLists.map(list => {
            const taskCount = tasks.filter(t => !t.isCompleted && t.listId === list.id).length;
            
            return `
                <div class="list-item" data-list-id="${list.id}" role="button" tabindex="0">
                    <span class="icon" aria-hidden="true">${list.icon || '📁'}</span>
                    <span>${this.escapeHtml(list.name)}</span>
                    <span class="list-count">${taskCount}</span>
                    <button class="list-delete task-action-btn" data-tooltip="Delete List" onclick="event.stopPropagation(); todoApp.deleteList('${list.id}')" aria-label="Delete list ${list.name}">🗑️</button>
                </div>
            `;
        }).join('');
    }
    
    renderTheme() {
        const { theme } = this.app.state;
        document.documentElement.setAttribute('data-theme', theme);
        this.dom.themeToggle.textContent = theme === 'light' ? '🌙' : '☀️';
    }

    updateSortSelect() {
        this.dom.sortSelect.value = this.app.state.sortBy;
    }

    focusTaskInput() {
        this.dom.taskInput.focus();
    }

    focusSearch() {
        this.dom.searchInput.focus();
    }

    showKeyboardShortcuts() {
        this.dom.shortcutsModal.overlay.style.display = 'flex';
    }

    closeKeyboardShortcuts() {
        this.dom.shortcutsModal.overlay.style.display = 'none';
    }

    showConflictResolution(conflict) {
        this.currentConflict = conflict;
        const modal = this.dom.conflictModal;

        // Set description
        modal.descriptionText.textContent = this.app.conflictResolver.getConflictDescription(conflict);

        // Show local data
        this.renderConflictData(modal.localData, conflict.local, 'Local');

        // Show server data
        this.renderConflictData(modal.serverData, conflict.server, 'Server');

        // Reset button states
        this.resetConflictButtons();

        // Show recommended strategy
        const recommended = this.app.conflictResolver.getRecommendedStrategy(conflict);
        if (recommended === 'merge') {
            modal.mergeBtn.classList.add('selected');
            modal.resolveBtn.disabled = false;
            this.selectedResolution = 'merge';
        }

        modal.overlay.style.display = 'flex';
    }

    renderConflictData(container, versionedData, label) {
        const preview = container.querySelector('.data-preview');
        const timestamp = container.querySelector('.data-timestamp');

        // Format data for display
        let displayData = versionedData.data;
        if (Array.isArray(displayData)) {
            displayData = `${displayData.length} items:\n` +
                displayData.slice(0, 3).map(item =>
                    `• ${item.title || item.name || JSON.stringify(item).substring(0, 50)}`
                ).join('\n') +
                (displayData.length > 3 ? `\n... and ${displayData.length - 3} more` : '');
        } else if (typeof displayData === 'object') {
            displayData = JSON.stringify(displayData, null, 2);
        } else {
            displayData = String(displayData);
        }

        preview.textContent = displayData;
        timestamp.textContent = `${label} • Modified: ${new Date(versionedData.lastModified).toLocaleString()}`;
    }

    resetConflictButtons() {
        const modal = this.dom.conflictModal;
        [modal.useLocalBtn, modal.useServerBtn, modal.mergeBtn].forEach(btn => {
            btn.classList.remove('selected');
        });
        modal.resolveBtn.disabled = true;
        this.selectedResolution = null;
    }

    selectResolution(resolution) {
        this.selectedResolution = resolution;
        this.resetConflictButtons();

        const modal = this.dom.conflictModal;
        const buttons = {
            'local-wins': modal.useLocalBtn,
            'server-wins': modal.useServerBtn,
            'merge': modal.mergeBtn
        };

        if (buttons[resolution]) {
            buttons[resolution].classList.add('selected');
            modal.resolveBtn.disabled = false;
        }
    }

    resolveCurrentConflict() {
        if (!this.currentConflict || !this.selectedResolution) return;

        const resolution = this.app.conflictResolver.resolveConflictAuto(
            this.currentConflict,
            this.selectedResolution
        );

        // Apply resolution
        this.app.applyConflictResolution(this.currentConflict, resolution);

        // Handle auto-resolve preference
        if (this.dom.conflictModal.autoResolveCheckbox.checked) {
            this.app.setAutoResolveStrategy(this.currentConflict.key, this.selectedResolution);
        }

        this.closeConflictModal();
    }

    closeConflictModal() {
        this.dom.conflictModal.overlay.style.display = 'none';
        this.currentConflict = null;
        this.selectedResolution = null;
    }

    updateSyncStatus(status, conflictCount = 0) {
        const sync = this.dom.syncStatus;

        // Update sync indicator
        sync.container.className = `sync-status ${status}`;

        switch (status) {
            case 'syncing':
                sync.icon.textContent = '⟳';
                sync.text.textContent = 'Syncing...';
                break;
            case 'success':
                sync.icon.textContent = '✓';
                sync.text.textContent = 'Synced';
                break;
            case 'error':
                sync.icon.textContent = '⚠';
                sync.text.textContent = 'Sync Error';
                break;
            case 'offline':
                sync.icon.textContent = '📴';
                sync.text.textContent = 'Offline';
                break;
            default:
                sync.icon.textContent = '✓';
                sync.text.textContent = 'Synced';
        }

        // Update conflict badge
        if (conflictCount > 0) {
            sync.conflictBadge.style.display = 'flex';
            sync.conflictCount.textContent = conflictCount;
        } else {
            sync.conflictBadge.style.display = 'none';
        }
    }

    renderTaskCount() {
        const count = this.app.getFilteredTasks().length;
        this.dom.taskCount.textContent = `${count} task${count !== 1 ? 's' : ''}`;
    }

    renderAnalytics() {
        const stats = this.app.getTaskStats();
        document.getElementById('stat-total').textContent = stats.total;
        document.getElementById('stat-completed').textContent = stats.completed;
        document.getElementById('stat-important').textContent = stats.important;
        document.getElementById('stat-overdue').textContent = stats.overdue;
        document.getElementById('stat-today').textContent = stats.completedToday;
    }

    updateCounts() {
        this.renderListCounts();
        this.renderTaskCount();
    }

    renderListCounts() {
        const { tasks } = this.app.state;
        const todayStr = new Date().toDateString();
        
        this.dom.allCount.textContent = tasks.filter(t => !t.isCompleted).length;
        this.dom.myDayCount.textContent = tasks.filter(t => !t.isCompleted && t.myDayDate === todayStr).length;
        this.dom.importantCount.textContent = tasks.filter(t => !t.isCompleted && t.isImportant).length;
        this.dom.plannedCount.textContent = tasks.filter(t => !t.isCompleted && t.dueDate).length;
    }

    updateListTitle() {
        const { currentList } = this.app.state;
        const titles = { all: 'All Tasks', 'my-day': 'My Day', important: 'Important', planned: 'Planned' };
        
        if (currentList.startsWith('user-')) {
            const userId = currentList.replace('user-', '');
            const user = this.app.state.users?.find(u => u.id === userId);
            this.dom.listTitle.textContent = user ? `${user.name}'s Tasks` : 'User Tasks';
        } else if (titles[currentList]) {
            this.dom.listTitle.textContent = titles[currentList];
        } else {
            const list = this.app.state.lists?.find(l => l.id === currentList);
            this.dom.listTitle.textContent = list ? list.name : 'All Tasks';
        }
    }
    
    escapeHtml(text) {
        if (!text) return '';
        return text.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#039;");
    }
    
    formatDate(date) {
        const today = new Date(); today.setHours(0,0,0,0);
        const tomorrow = new Date(today); tomorrow.setDate(tomorrow.getDate() + 1);
        const yesterday = new Date(today); yesterday.setDate(yesterday.getDate() - 1);
        
        const compareDate = new Date(date); compareDate.setHours(0,0,0,0);

        if (compareDate.getTime() === today.getTime()) return 'Today';
        if (compareDate.getTime() === tomorrow.getTime()) return 'Tomorrow';
        if (compareDate.getTime() === yesterday.getTime()) return 'Yesterday';
        return date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
    }
    
    openEditModalById(taskId) {
        const task = this.app.state.tasks.find(t => t.id === taskId);
        if (task) this.openEditModal(task);
    }

    openEditModal(task) {
        this.currentEditTask = task;
        const modal = this.dom.editModal;

        modal.titleInput.value = task.title || '';
        modal.notesTextarea.value = task.notes || '';
        modal.dueDateInput.value = task.dueDate || '';
        modal.recurrenceSelect.value = task.recurrence || '';

        const { users } = this.app.state;
        const assignedIds = new Set((task.assignees || []).map(a => a.id));
        modal.assigneeList.innerHTML = users.map(user => {
            const isSelected = assignedIds.has(user.id);
            const initial = user.name ? user.name.trim().split(' ')[0][0].toUpperCase() : '?';
            return `
                <div class="assignee-item ${isSelected ? 'selected' : ''}" data-user-id="${user.id}" role="option" aria-selected="${isSelected}" tabindex="0">
                    <div class="user-avatar" style="background-color: ${user.avatar}" aria-hidden="true">
                        ${initial}
                    </div>
                    <span>${this.escapeHtml(user.name)}</span>
                    <span class="checkmark">${isSelected ? '✓' : ''}</span>
                </div>
            `;
        }).join('');
        
        modal.overlay.classList.add('show');
        modal.titleInput.focus();
    }

    closeEditModal() {
        this.dom.editModal.overlay.classList.remove('show');
        this.currentEditTask = null;
    }

    toggleTaskSelection(taskId) {
        if (this.selectedTasks.has(taskId)) {
            this.selectedTasks.delete(taskId);
        } else {
            this.selectedTasks.add(taskId);
        }
        this.updateBulkActions();
        this.renderTasks(); // Re-render to show selection change
    }
    
    startBulkMode() {
        this.bulkMode = true;
        this.renderTasks();
    }

    updateBulkActions() {
        const { container, info } = this.dom.bulkActions;
        const size = this.selectedTasks.size;
        if (size > 0) {
            container.classList.add('show');
            info.textContent = `${size} task${size !== 1 ? 's' : ''} selected`;
        } else {
            container.classList.remove('show');
            this.bulkMode = false;
        }
    }
    
    cancelBulkMode() {
        this.selectedTasks.clear();
        this.bulkMode = false;
        this.renderTasks();
        this.updateBulkActions();
    }

    setActiveListItem(listId) {
        document.querySelectorAll('.list-item, .user-item').forEach(i => i.classList.remove('active'));
        const selector = listId.startsWith('user-') ? `[data-user-id="${listId.replace('user-','')}"]` : `[data-list="${listId}"], [data-list-id="${listId}"]`;
        const activeItem = document.querySelector(selector);
        if (activeItem) activeItem.classList.add('active');
    }

    setupEventListeners() {
        // Task form
        document.getElementById('task-form').addEventListener('submit', (e) => {
            e.preventDefault();
            const titleInput = this.dom.taskInput;
            const dateInput = document.getElementById('new-task-date');
            const title = titleInput.value.trim();
            if (title) {
                this.app.addTask({ title, dueDate: dateInput.value || undefined });
                titleInput.value = '';
                dateInput.value = '';
                titleInput.focus();
            }
        });

        // Sidebar navigation
        document.querySelector('.sidebar').addEventListener('click', e => {
            const listItem = e.target.closest('.list-item');
            const userItem = e.target.closest('.user-item');
            if (listItem) {
                const listId = listItem.dataset.list || listItem.dataset.listId;
                this.app.switchList(listId);
            } else if (userItem) {
                const userId = userItem.dataset.userId;
                this.app.switchList(`user-${userId}`);
            }
        });

        // New list form
        document.getElementById('new-list-form').addEventListener('submit', async e => {
            e.preventDefault();
            console.log('New list form submitted');
            const input = document.getElementById('new-list-input');
            if (input.value.trim()) {
                console.log('Creating list:', input.value.trim());
                try {
                    await this.app.addList(input.value.trim());
                    input.value = '';
                    console.log('List created successfully');
                } catch (error) {
                    console.error('Failed to create list:', error);
                    // Keep the input value so user can try again
                }
            }
        });

        // Also handle Enter key on the input field directly
        document.getElementById('new-list-input').addEventListener('keydown', async e => {
            if (e.key === 'Enter') {
                e.preventDefault();
                console.log('Enter key pressed on new list input');
                const input = e.target;
                if (input.value.trim()) {
                    console.log('Creating list via Enter key:', input.value.trim());
                    try {
                        await this.app.addList(input.value.trim());
                        input.value = '';
                        console.log('List created successfully via Enter key');
                    } catch (error) {
                        console.error('Failed to create list:', error);
                        // Keep the input value so user can try again
                    }
                }
            }
        });

        // Theme toggle
        this.dom.themeToggle.addEventListener('click', () => this.app.toggleTheme());

        // Help button
        this.dom.helpButton.addEventListener('click', () => this.showKeyboardShortcuts());
        // Search
        this.dom.searchInput.addEventListener('input', e => this.app.setSearchTerm(e.target.value));

        // Sort
        this.dom.sortSelect.addEventListener('change', e => this.app.setSortBy(e.target.value));
        
        // Toolbar
        const toolbar = this.dom.toolbar;
        toolbar.saveBtn.addEventListener('click', () => this.app.saveData());
        toolbar.loadBtn.addEventListener('click', () => this.app.loadDataFromFile());
        toolbar.exportCsvBtn.addEventListener('click', () => this.app.exportCsv());
        toolbar.printBtn.addEventListener('click', () => this.app.printTasks());
        toolbar.clearBtn.addEventListener('click', () => this.app.clearCurrentTasks());

        // Modal
        const modal = this.dom.editModal;
        modal.saveBtn.addEventListener('click', () => {
            if (!this.currentEditTask) return;
            const title = modal.titleInput.value.trim();
            if (!title) return;
            const notes = modal.notesTextarea.value.trim();
            const dueDate = modal.dueDateInput.value || undefined;
            const recurrence = modal.recurrenceSelect.value || undefined;

            const selectedAssigneeNodes = modal.assigneeList.querySelectorAll('.assignee-item.selected');
            const assignees = Array.from(selectedAssigneeNodes).map(node => {
                const userId = node.dataset.userId;
                const user = this.app.state.users.find(u => u.id === userId);
                return { id: user.id, name: user.name, avatar: user.avatar };
            });

            this.app.updateTask(this.currentEditTask.id, { title, notes, dueDate, recurrence, assignees });
            this.closeEditModal();
        });
        modal.cancelBtn.addEventListener('click', () => this.closeEditModal());
        modal.closeBtn.addEventListener('click', () => this.closeEditModal());
        modal.overlay.addEventListener('click', e => { if (e.target === modal.overlay) this.closeEditModal() });

        // Shortcuts modal
        const shortcutsModal = this.dom.shortcutsModal;
        shortcutsModal.closeBtn.addEventListener('click', () => this.closeKeyboardShortcuts());
        shortcutsModal.gotItBtn.addEventListener('click', () => this.closeKeyboardShortcuts());
        shortcutsModal.overlay.addEventListener('click', e => { if (e.target === shortcutsModal.overlay) this.closeKeyboardShortcuts() });

        // Conflict resolution modal
        const conflictModal = this.dom.conflictModal;
        conflictModal.closeBtn.addEventListener('click', () => this.closeConflictModal());
        conflictModal.cancelBtn.addEventListener('click', () => this.closeConflictModal());
        conflictModal.resolveBtn.addEventListener('click', () => this.resolveCurrentConflict());
        conflictModal.overlay.addEventListener('click', e => { if (e.target === conflictModal.overlay) this.closeConflictModal() });

        // Resolution option buttons
        conflictModal.useLocalBtn.addEventListener('click', () => this.selectResolution('local-wins'));
        conflictModal.useServerBtn.addEventListener('click', () => this.selectResolution('server-wins'));
        conflictModal.mergeBtn.addEventListener('click', () => this.selectResolution('merge'));

        // Conflict badge click
        this.dom.syncStatus.conflictBadge.addEventListener('click', () => this.app.showNextConflict());
        
        // Custom assignee select in modal
        modal.assigneeList.addEventListener('click', e => {
            const item = e.target.closest('.assignee-item');
            if (!item) return;

            item.classList.toggle('selected');
            const isSelected = item.classList.contains('selected');
            item.setAttribute('aria-selected', String(isSelected));
            item.querySelector('.checkmark').textContent = isSelected ? '✓' : '';
        });

        // Bulk Actions
        const bulk = this.dom.bulkActions;
        bulk.completeBtn.addEventListener('click', () => {
            this.selectedTasks.forEach(id => this.app.toggleTaskCompletion(id, true));
            this.cancelBulkMode();
        });
        bulk.deleteBtn.addEventListener('click', () => {
            if (confirm(`Delete ${this.selectedTasks.size} tasks?`)) {
                this.selectedTasks.forEach(id => this.app.deleteTask(id, true));
                this.cancelBulkMode();
            }
        });
        bulk.cancelBtn.addEventListener('click', () => this.cancelBulkMode());

        // Setup drag and drop
        this.setupDragAndDrop();
    }

    setupDragAndDrop() {
        let draggedTask = null;
        let draggedOverElement = null;

        // Task list drag and drop
        this.dom.taskList.addEventListener('dragstart', (e) => {
            const taskItem = e.target.closest('.task-item');
            if (!taskItem) return;

            draggedTask = taskItem.dataset.taskId;
            taskItem.classList.add('dragging');
            e.dataTransfer.effectAllowed = 'move';
            e.dataTransfer.setData('text/plain', draggedTask);
        });

        this.dom.taskList.addEventListener('dragend', (e) => {
            const taskItem = e.target.closest('.task-item');
            if (taskItem) {
                taskItem.classList.remove('dragging');
            }
            // Clean up drag over effects
            document.querySelectorAll('.drag-over').forEach(el => el.classList.remove('drag-over'));
            draggedTask = null;
            draggedOverElement = null;
        });

        this.dom.taskList.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';

            const taskItem = e.target.closest('.task-item');
            if (taskItem && taskItem.dataset.taskId !== draggedTask) {
                // Remove previous drag-over effects
                document.querySelectorAll('.task-item.drag-over').forEach(el => el.classList.remove('drag-over'));

                // Add drag-over effect to current item
                taskItem.classList.add('drag-over');
                draggedOverElement = taskItem;
            }
        });

        this.dom.taskList.addEventListener('drop', (e) => {
            e.preventDefault();

            const targetTaskItem = e.target.closest('.task-item');
            if (targetTaskItem && draggedTask && targetTaskItem.dataset.taskId !== draggedTask) {
                const targetTaskId = targetTaskItem.dataset.taskId;
                this.app.reorderTasks(draggedTask, targetTaskId);
            }

            // Clean up
            document.querySelectorAll('.drag-over').forEach(el => el.classList.remove('drag-over'));
        });

        // Sidebar list drag and drop
        document.querySelector('.sidebar').addEventListener('dragover', (e) => {
            const listItem = e.target.closest('.list-item');
            if (listItem && draggedTask) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';

                // Remove previous drag-over effects
                document.querySelectorAll('.list-item.drag-over').forEach(el => el.classList.remove('drag-over'));
                listItem.classList.add('drag-over');
            }
        });

        document.querySelector('.sidebar').addEventListener('drop', (e) => {
            const listItem = e.target.closest('.list-item');
            if (listItem && draggedTask) {
                e.preventDefault();

                const targetListId = listItem.dataset.list || listItem.dataset.listId;
                if (targetListId) {
                    this.app.moveTaskToList(draggedTask, targetListId);
                }
            }

            // Clean up
            document.querySelectorAll('.list-item.drag-over').forEach(el => el.classList.remove('drag-over'));
        });
    }
}

