<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Todo API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 40px auto;
            padding: 0 20px;
            line-height: 1.6;
        }
        .container {
            background-color: #f5f5f5;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .api-links {
            margin-top: 20px;
        }
        .api-links a {
            display: inline-block;
            margin: 10px;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        .api-links a:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Welcome to Todo API</h1>
        <p>This is a RESTful API built with ASP.NET Core that provides various endpoints for managing todos and weather forecasts.</p>
        
        <div class="api-links">
            <a href="/swagger">API Documentation</a>
            <a href="/weatherforecast">Weather Forecast</a>
        </div>
        
        <h2>Available Endpoints:</h2>
        <ul>
            <li><strong>/weatherforecast</strong> - Get weather forecast data</li>
            <li><strong>/swagger</strong> - Interactive API documentation</li>
            <li><strong>/swagger/v1/swagger.json</strong> - OpenAPI specification</li>
        </ul>
    </div>
</body>
</html>