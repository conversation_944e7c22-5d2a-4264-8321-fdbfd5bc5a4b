[{"ContainingType": "todoapi.Controllers.ListsController", "Method": "GetAllLists", "RelativePath": "api/Lists", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "todoapi.DTOs.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[todoapi.DTOs.TodoListDto, todoapi, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "todoapi.Controllers.ListsController", "Method": "CreateList", "RelativePath": "api/Lists", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "listDto", "Type": "todoapi.DTOs.CreateTodoListDto", "IsRequired": true}], "ReturnTypes": [{"Type": "todoapi.DTOs.ApiResponse`1[[todoapi.DTOs.TodoListDto, todoapi, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "todoapi.Controllers.ListsController", "Method": "GetList", "RelativePath": "api/Lists/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "todoapi.DTOs.ApiResponse`1[[todoapi.DTOs.TodoListDto, todoapi, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "todoapi.Controllers.ListsController", "Method": "UpdateList", "RelativePath": "api/Lists/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "listDto", "Type": "todoapi.DTOs.CreateTodoListDto", "IsRequired": true}], "ReturnTypes": [{"Type": "todoapi.DTOs.ApiResponse`1[[todoapi.DTOs.TodoListDto, todoapi, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "todoapi.Controllers.ListsController", "Method": "DeleteList", "RelativePath": "api/Lists/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "todoapi.DTOs.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "todoapi.Controllers.StatsController", "Method": "GetStats", "RelativePath": "api/Stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "todoapi.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "todoapi.Controllers.TasksController", "Method": "GetAllTasks", "RelativePath": "api/Tasks", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "todoapi.DTOs.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[todoapi.DTOs.TodoTaskDto, todoapi, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "todoapi.Controllers.TasksController", "Method": "CreateTask", "RelativePath": "api/Tasks", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskDto", "Type": "todoapi.DTOs.CreateTodoTaskDto", "IsRequired": true}], "ReturnTypes": [{"Type": "todoapi.DTOs.ApiResponse`1[[todoapi.DTOs.TodoTaskDto, todoapi, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "todoapi.Controllers.TasksController", "Method": "GetTask", "RelativePath": "api/Tasks/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "todoapi.DTOs.ApiResponse`1[[todoapi.DTOs.TodoTaskDto, todoapi, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "todoapi.Controllers.TasksController", "Method": "UpdateTask", "RelativePath": "api/Tasks/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "taskDto", "Type": "todoapi.DTOs.UpdateTodoTaskDto", "IsRequired": true}], "ReturnTypes": [{"Type": "todoapi.DTOs.ApiResponse`1[[todoapi.DTOs.TodoTaskDto, todoapi, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "todoapi.Controllers.TasksController", "Method": "DeleteTask", "RelativePath": "api/Tasks/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "todoapi.DTOs.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "todoapi.Controllers.TasksController", "Method": "BulkOperation", "RelativePath": "api/Tasks/bulk", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "bulkDto", "Type": "todoapi.DTOs.BulkOperationDto", "IsRequired": true}], "ReturnTypes": [{"Type": "todoapi.DTOs.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "todoapi.Controllers.TasksController", "Method": "GetTasksByList", "RelativePath": "api/Tasks/by-list/{listId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "listId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "todoapi.DTOs.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[todoapi.DTOs.TodoTaskDto, todoapi, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "todoapi.Controllers.TasksController", "Method": "GetTasksByUser", "RelativePath": "api/Tasks/by-user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "todoapi.DTOs.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[todoapi.DTOs.TodoTaskDto, todoapi, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "todoapi.Controllers.TasksController", "Method": "GetImportantTasks", "RelativePath": "api/Tasks/important", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "todoapi.DTOs.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[todoapi.DTOs.TodoTaskDto, todoapi, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "todoapi.Controllers.TasksController", "Method": "GetMyDayTasks", "RelativePath": "api/Tasks/my-day/{date}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "date", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "todoapi.DTOs.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[todoapi.DTOs.TodoTaskDto, todoapi, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "todoapi.Controllers.TasksController", "Method": "GetPlannedTasks", "RelativePath": "api/Tasks/planned", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "todoapi.DTOs.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[todoapi.DTOs.TodoTaskDto, todoapi, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "todoapi.Controllers.UsersController", "Method": "GetAllUsers", "RelativePath": "api/Users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "todoapi.DTOs.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[todoapi.DTOs.UserDto, todoapi, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "todoapi.Controllers.UsersController", "Method": "CreateUser", "RelativePath": "api/Users", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userDto", "Type": "todoapi.DTOs.CreateUserDto", "IsRequired": true}], "ReturnTypes": [{"Type": "todoapi.DTOs.ApiResponse`1[[todoapi.DTOs.UserDto, todoapi, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "todoapi.Controllers.UsersController", "Method": "GetUser", "RelativePath": "api/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "todoapi.DTOs.ApiResponse`1[[todoapi.DTOs.UserDto, todoapi, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "todoapi.Controllers.UsersController", "Method": "UpdateUser", "RelativePath": "api/Users/<USER>", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "userDto", "Type": "todoapi.DTOs.CreateUserDto", "IsRequired": true}], "ReturnTypes": [{"Type": "todoapi.DTOs.ApiResponse`1[[todoapi.DTOs.UserDto, todoapi, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "todoapi.Controllers.UsersController", "Method": "DeleteUser", "RelativePath": "api/Users/<USER>", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "todoapi.DTOs.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "todoapi.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[todoapi.WeatherForecast, todoapi, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}]