{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "index.5lbvunm6pn.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001305483029"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "765"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"fL6DYJfZe8Kh5PgiL0pbcziLtCpR2DefJfiZd+wDREc=\""}, {"Name": "ETag", "Value": "W/\"lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 11:42:02 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5lbvunm6pn"}, {"Name": "integrity", "Value": "sha256-lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.5lbvunm6pn.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1941"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 03:04:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5lbvunm6pn"}, {"Name": "integrity", "Value": "sha256-lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.5lbvunm6pn.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "765"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"fL6DYJfZe8Kh5PgiL0pbcziLtCpR2DefJfiZd+wDREc=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 11:42:02 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5lbvunm6pn"}, {"Name": "integrity", "Value": "sha256-fL6DYJfZe8Kh5PgiL0pbcziLtCpR2DefJfiZd+wDREc="}, {"Name": "label", "Value": "index.html.gz"}]}, {"Route": "index.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001305483029"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "765"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"fL6DYJfZe8Kh5PgiL0pbcziLtCpR2DefJfiZd+wDREc=\""}, {"Name": "ETag", "Value": "W/\"lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 11:42:02 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1941"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 03:04:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ="}]}, {"Route": "index.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "765"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"fL6DYJfZe8Kh5PgiL0pbcziLtCpR2DefJfiZd+wDREc=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 11:42:02 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fL6DYJfZe8Kh5PgiL0pbcziLtCpR2DefJfiZd+wDREc="}]}, {"Route": "index9.16oku2ib4b.html", "AssetFile": "index9.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000053013837"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "18862"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"wJ6sk1NZvhl6Z3pn4pBrv6Wed46Rqy2TwWVpFpBS+k4=\""}, {"Name": "ETag", "Value": "W/\"p0KvelnDSwL8iAS6MAwBF6Oxcmf79vfFndKrWXfqEuk=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 12:35:28 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "16oku2ib4b"}, {"Name": "integrity", "Value": "sha256-p0KvelnDSwL8iAS6MAwBF6Oxcmf79vfFndKrWXfqEuk="}, {"Name": "label", "Value": "index9.html"}]}, {"Route": "index9.16oku2ib4b.html", "AssetFile": "index9.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "118816"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"p0KvelnDSwL8iAS6MAwBF6Oxcmf79vfFndKrWXfqEuk=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 12:34:02 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "16oku2ib4b"}, {"Name": "integrity", "Value": "sha256-p0KvelnDSwL8iAS6MAwBF6Oxcmf79vfFndKrWXfqEuk="}, {"Name": "label", "Value": "index9.html"}]}, {"Route": "index9.16oku2ib4b.html.gz", "AssetFile": "index9.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "18862"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"wJ6sk1NZvhl6Z3pn4pBrv6Wed46Rqy2TwWVpFpBS+k4=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 12:35:28 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "16oku2ib4b"}, {"Name": "integrity", "Value": "sha256-wJ6sk1NZvhl6Z3pn4pBrv6Wed46Rqy2TwWVpFpBS+k4="}, {"Name": "label", "Value": "index9.html.gz"}]}, {"Route": "index9.html", "AssetFile": "index9.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000053013837"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "18862"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"wJ6sk1NZvhl6Z3pn4pBrv6Wed46Rqy2TwWVpFpBS+k4=\""}, {"Name": "ETag", "Value": "W/\"p0KvelnDSwL8iAS6MAwBF6Oxcmf79vfFndKrWXfqEuk=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 12:35:28 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p0KvelnDSwL8iAS6MAwBF6Oxcmf79vfFndKrWXfqEuk="}]}, {"Route": "index9.html", "AssetFile": "index9.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "118816"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"p0KvelnDSwL8iAS6MAwBF6Oxcmf79vfFndKrWXfqEuk=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 12:34:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p0KvelnDSwL8iAS6MAwBF6Oxcmf79vfFndKrWXfqEuk="}]}, {"Route": "index9.html.gz", "AssetFile": "index9.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "18862"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"wJ6sk1NZvhl6Z3pn4pBrv6Wed46Rqy2TwWVpFpBS+k4=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 12:35:28 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wJ6sk1NZvhl6Z3pn4pBrv6Wed46Rqy2TwWVpFpBS+k4="}]}]}