using Microsoft.EntityFrameworkCore;
using todoapi.Models;

namespace todoapi.Data;

public class TodoDbContext : DbContext
{
    public TodoDbContext(DbContextOptions<TodoDbContext> options) : base(options)
    {
    }

    public DbSet<TodoTask> Tasks { get; set; }
    public DbSet<TodoList> Lists { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<TaskAssignee> TaskAssignees { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Configure TodoTask
        modelBuilder.Entity<TodoTask>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Title).IsRequired().HasMaxLength(500);
            entity.Property(e => e.Notes).HasMaxLength(2000);
            entity.Property(e => e.CreatedAt).IsRequired();
            
            // Foreign key to TodoList
            entity.HasOne(e => e.List)
                  .WithMany(l => l.Tasks)
                  .HasForeignKey(e => e.ListId)
                  .OnDelete(DeleteBehavior.SetNull);
        });

        // Configure TodoList
        modelBuilder.Entity<TodoList>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Icon).HasMaxLength(10);
            entity.Property(e => e.CreatedAt).IsRequired();
        });

        // Configure User
        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Email).IsRequired().HasMaxLength(300);
            entity.Property(e => e.Avatar).HasMaxLength(50);
            entity.Property(e => e.CreatedAt).IsRequired();
            
            // Unique email constraint
            entity.HasIndex(e => e.Email).IsUnique();
        });

        // Configure TaskAssignee (many-to-many relationship)
        modelBuilder.Entity<TaskAssignee>(entity =>
        {
            entity.HasKey(e => new { e.TaskId, e.UserId });
            
            entity.HasOne(e => e.Task)
                  .WithMany(t => t.Assignees)
                  .HasForeignKey(e => e.TaskId)
                  .OnDelete(DeleteBehavior.Cascade);
                  
            entity.HasOne(e => e.User)
                  .WithMany(u => u.TaskAssignments)
                  .HasForeignKey(e => e.UserId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Seed default data
        SeedData(modelBuilder);
    }

    private static void SeedData(ModelBuilder modelBuilder)
    {
        // Seed default users
        var users = new[]
        {
            new User { Id = "1", Name = "Dad", Email = "<EMAIL>", Avatar = "#4CAF50", CreatedAt = DateTime.UtcNow },
            new User { Id = "2", Name = "Mom", Email = "<EMAIL>", Avatar = "#2196F3", CreatedAt = DateTime.UtcNow },
            new User { Id = "3", Name = "Jr. John", Email = "<EMAIL>", Avatar = "#FF9800", CreatedAt = DateTime.UtcNow },
            new User { Id = "4", Name = "Mike", Email = "<EMAIL>", Avatar = "#9C27B0", CreatedAt = DateTime.UtcNow },
            new User { Id = "5", Name = "Louie", Email = "<EMAIL>", Avatar = "#E91E63", CreatedAt = DateTime.UtcNow }
        };
        modelBuilder.Entity<User>().HasData(users);

        // Seed default lists
        var lists = new[]
        {
            new TodoList { Id = "grocery", Name = "Grocery List", Icon = "??", Smart = false, CreatedAt = DateTime.UtcNow },
            new TodoList { Id = "work", Name = "Work Tasks", Icon = "??", Smart = false, CreatedAt = DateTime.UtcNow },
            new TodoList { Id = "personal", Name = "Personal", Icon = "??", Smart = false, CreatedAt = DateTime.UtcNow }
        };
        modelBuilder.Entity<TodoList>().HasData(lists);

        // Seed some sample tasks
        var tasks = new[]
        {
            new TodoTask 
            { 
                Id = "1", 
                Title = "Buy milk and bread", 
                Notes = "From the corner store", 
                ListId = "grocery",
                CreatedAt = DateTime.UtcNow,
                DueDate = DateTime.UtcNow.AddDays(1)
            },
            new TodoTask 
            { 
                Id = "2", 
                Title = "Complete project documentation", 
                Notes = "Update README and API docs", 
                ListId = "work",
                IsImportant = true,
                CreatedAt = DateTime.UtcNow.AddHours(-2),
                DueDate = DateTime.UtcNow.AddDays(3)
            },
            new TodoTask 
            { 
                Id = "3", 
                Title = "Call dentist for appointment", 
                ListId = "personal",
                CreatedAt = DateTime.UtcNow.AddHours(-5),
                MyDayDate = DateTime.UtcNow.ToString("yyyy-MM-dd")
            }
        };
        modelBuilder.Entity<TodoTask>().HasData(tasks);

        // Seed task assignments
        var assignments = new[]
        {
            new TaskAssignee { TaskId = "1", UserId = "2", AssignedAt = DateTime.UtcNow }, // Mom assigned to grocery
            new TaskAssignee { TaskId = "2", UserId = "1", AssignedAt = DateTime.UtcNow }, // Dad assigned to work
            new TaskAssignee { TaskId = "3", UserId = "3", AssignedAt = DateTime.UtcNow }  // Jr. John assigned to personal
        };
        modelBuilder.Entity<TaskAssignee>().HasData(assignments);
    }
}