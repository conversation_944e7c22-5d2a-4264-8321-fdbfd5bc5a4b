M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\testhost.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\testhost.exe
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\xunit.runner.visualstudio.testadapter.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\xunit.runner.reporters.netcoreapp10.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\xunit.runner.utility.netcoreapp10.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\TestProject.deps.json
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\TestProject.runtimeconfig.json
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\TestProject.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\TestProject.pdb
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.VisualStudio.CodeCoverage.Shim.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.TestPlatform.CoreUtilities.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.TestPlatform.PlatformAbstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.VisualStudio.TestPlatform.ObjectModel.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.TestPlatform.CommunicationUtilities.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.TestPlatform.CrossPlatEngine.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.TestPlatform.Utilities.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.VisualStudio.TestPlatform.Common.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Newtonsoft.Json.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\xunit.abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\xunit.assert.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\xunit.core.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\xunit.execution.dotnet.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\cs\Microsoft.TestPlatform.CoreUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\cs\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\de\Microsoft.TestPlatform.CoreUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\de\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\es\Microsoft.TestPlatform.CoreUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\es\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\fr\Microsoft.TestPlatform.CoreUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\fr\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\it\Microsoft.TestPlatform.CoreUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\it\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\ja\Microsoft.TestPlatform.CoreUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\ja\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\ko\Microsoft.TestPlatform.CoreUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\ko\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\pl\Microsoft.TestPlatform.CoreUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\pl\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\pt-BR\Microsoft.TestPlatform.CoreUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\pt-BR\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\ru\Microsoft.TestPlatform.CoreUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\ru\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\tr\Microsoft.TestPlatform.CoreUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\tr\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\zh-Hans\Microsoft.TestPlatform.CoreUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\zh-Hans\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\zh-Hant\Microsoft.TestPlatform.CoreUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\zh-Hant\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\cs\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\cs\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\cs\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\de\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\de\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\de\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\es\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\es\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\es\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\fr\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\fr\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\fr\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\it\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\it\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\it\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\ja\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\ja\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\ja\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\ko\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\ko\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\ko\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\pl\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\pl\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\pl\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\pt-BR\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\pt-BR\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\pt-BR\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\ru\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\ru\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\ru\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\tr\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\tr\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\tr\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\zh-Hans\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\zh-Hans\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\zh-Hans\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\zh-Hant\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\zh-Hant\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\zh-Hant\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\obj\Debug\net9.0\TestProject.csproj.AssemblyReference.cache
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\obj\Debug\net9.0\TestProject.GeneratedMSBuildEditorConfig.editorconfig
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\obj\Debug\net9.0\TestProject.AssemblyInfoInputs.cache
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\obj\Debug\net9.0\TestProject.AssemblyInfo.cs
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\obj\Debug\net9.0\TestProject.csproj.CoreCompileInputs.cache
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\obj\Debug\net9.0\TestProj.121896E9.Up2Date
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\obj\Debug\net9.0\TestProject.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\obj\Debug\net9.0\refint\TestProject.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\obj\Debug\net9.0\TestProject.pdb
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\obj\Debug\net9.0\TestProject.genruntimeconfig.cache
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\obj\Debug\net9.0\ref\TestProject.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\.msCoverageSourceRootsMapping_TestProject
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\CoverletSourceRootsMapping_TestProject
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\todoapi.deps.json
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\todoapi.runtimeconfig.json
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\appsettings.Development.json
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\appsettings.json
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\todoapi.staticwebassets.runtime.json
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\todoapi.staticwebassets.endpoints.json
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\todoapi.exe
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\MvcTestingAppManifest.json
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Antiforgery.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Authentication.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Authentication.BearerToken.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Authentication.Cookies.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Authentication.Core.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Authentication.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Authentication.OAuth.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Authorization.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Authorization.Policy.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Components.Authorization.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Components.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Components.Endpoints.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Components.Forms.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Components.Server.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Components.Web.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Connections.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.CookiePolicy.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Cors.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Cryptography.Internal.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Cryptography.KeyDerivation.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.DataProtection.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.DataProtection.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.DataProtection.Extensions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Diagnostics.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Diagnostics.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Diagnostics.HealthChecks.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.HostFiltering.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Hosting.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Hosting.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Hosting.Server.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Html.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Http.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Http.Connections.Common.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Http.Connections.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Http.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Http.Extensions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Http.Features.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Http.Results.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.HttpLogging.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.HttpOverrides.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.HttpsPolicy.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Identity.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Localization.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Localization.Routing.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Metadata.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Mvc.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Mvc.ApiExplorer.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Mvc.Core.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Mvc.Cors.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Mvc.DataAnnotations.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Mvc.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Mvc.Formatters.Json.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Mvc.Formatters.Xml.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Mvc.Localization.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Mvc.Razor.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Mvc.RazorPages.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Mvc.TagHelpers.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Mvc.ViewFeatures.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.OutputCaching.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.RateLimiting.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Razor.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Razor.Runtime.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.RequestDecompression.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.ResponseCaching.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.ResponseCaching.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.ResponseCompression.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Rewrite.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Routing.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Routing.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Server.HttpSys.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Server.IIS.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Server.IISIntegration.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Server.Kestrel.Core.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Server.Kestrel.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.Session.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.SignalR.Common.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.SignalR.Core.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.SignalR.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.SignalR.Protocols.Json.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.StaticAssets.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.StaticFiles.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.WebSockets.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.AspNetCore.WebUtilities.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.CSharp.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Caching.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Caching.Memory.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Configuration.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Configuration.Binder.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Configuration.CommandLine.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Configuration.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Configuration.EnvironmentVariables.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Configuration.FileExtensions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Configuration.Ini.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Configuration.Json.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Configuration.KeyPerFile.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Configuration.UserSecrets.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Configuration.Xml.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.DependencyInjection.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.DependencyInjection.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Diagnostics.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Diagnostics.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Diagnostics.HealthChecks.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Features.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.FileProviders.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.FileProviders.Composite.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.FileProviders.Embedded.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.FileProviders.Physical.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.FileSystemGlobbing.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Hosting.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Hosting.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Http.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Identity.Core.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Identity.Stores.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Localization.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Localization.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Logging.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Logging.Configuration.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Logging.Console.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Logging.Debug.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Logging.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Logging.EventLog.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Logging.EventSource.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Logging.TraceSource.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.ObjectPool.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Options.ConfigurationExtensions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Options.DataAnnotations.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Options.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.Primitives.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Extensions.WebEncoders.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.JSInterop.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Net.Http.Headers.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.VisualBasic.Core.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.VisualBasic.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Win32.Primitives.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\Microsoft.Win32.Registry.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\mscorlib.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\netstandard.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.AppContext.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Buffers.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Collections.Concurrent.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Collections.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Collections.Immutable.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Collections.NonGeneric.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Collections.Specialized.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.ComponentModel.Annotations.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.ComponentModel.DataAnnotations.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.ComponentModel.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.ComponentModel.EventBasedAsync.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.ComponentModel.Primitives.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.ComponentModel.TypeConverter.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Configuration.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Console.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Core.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Data.Common.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Data.DataSetExtensions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Data.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Diagnostics.Contracts.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Diagnostics.Debug.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Diagnostics.DiagnosticSource.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Diagnostics.EventLog.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Diagnostics.FileVersionInfo.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Diagnostics.Process.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Diagnostics.StackTrace.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Diagnostics.TextWriterTraceListener.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Diagnostics.Tools.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Diagnostics.TraceSource.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Diagnostics.Tracing.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Drawing.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Drawing.Primitives.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Dynamic.Runtime.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Formats.Asn1.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Formats.Tar.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Globalization.Calendars.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Globalization.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Globalization.Extensions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.IO.Compression.Brotli.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.IO.Compression.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.IO.Compression.FileSystem.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.IO.Compression.ZipFile.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.IO.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.IO.FileSystem.AccessControl.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.IO.FileSystem.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.IO.FileSystem.DriveInfo.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.IO.FileSystem.Primitives.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.IO.FileSystem.Watcher.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.IO.IsolatedStorage.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.IO.MemoryMappedFiles.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.IO.Pipelines.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.IO.Pipes.AccessControl.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.IO.Pipes.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.IO.UnmanagedMemoryStream.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Linq.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Linq.Expressions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Linq.Parallel.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Linq.Queryable.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Memory.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Net.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Net.Http.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Net.Http.Json.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Net.HttpListener.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Net.Mail.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Net.NameResolution.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Net.NetworkInformation.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Net.Ping.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Net.Primitives.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Net.Quic.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Net.Requests.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Net.Security.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Net.ServicePoint.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Net.Sockets.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Net.WebClient.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Net.WebHeaderCollection.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Net.WebProxy.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Net.WebSockets.Client.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Net.WebSockets.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Numerics.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Numerics.Vectors.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.ObjectModel.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Reflection.DispatchProxy.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Reflection.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Reflection.Emit.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Reflection.Emit.ILGeneration.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Reflection.Emit.Lightweight.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Reflection.Extensions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Reflection.Metadata.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Reflection.Primitives.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Reflection.TypeExtensions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Resources.Reader.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Resources.ResourceManager.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Resources.Writer.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Runtime.CompilerServices.Unsafe.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Runtime.CompilerServices.VisualC.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Runtime.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Runtime.Extensions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Runtime.Handles.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Runtime.InteropServices.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Runtime.InteropServices.JavaScript.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Runtime.InteropServices.RuntimeInformation.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Runtime.Intrinsics.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Runtime.Loader.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Runtime.Numerics.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Runtime.Serialization.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Runtime.Serialization.Formatters.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Runtime.Serialization.Json.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Runtime.Serialization.Primitives.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Runtime.Serialization.Xml.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Security.AccessControl.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Security.Claims.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Security.Cryptography.Algorithms.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Security.Cryptography.Cng.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Security.Cryptography.Csp.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Security.Cryptography.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Security.Cryptography.Encoding.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Security.Cryptography.OpenSsl.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Security.Cryptography.Primitives.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Security.Cryptography.X509Certificates.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Security.Cryptography.Xml.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Security.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Security.Principal.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Security.Principal.Windows.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Security.SecureString.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.ServiceModel.Web.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.ServiceProcess.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Text.Encoding.CodePages.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Text.Encoding.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Text.Encoding.Extensions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Text.Encodings.Web.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Text.Json.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Text.RegularExpressions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Threading.Channels.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Threading.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Threading.Overlapped.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Threading.RateLimiting.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Threading.Tasks.Dataflow.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Threading.Tasks.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Threading.Tasks.Extensions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Threading.Tasks.Parallel.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Threading.Thread.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Threading.ThreadPool.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Threading.Timer.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Transactions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Transactions.Local.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.ValueTuple.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Web.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Web.HttpUtility.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Windows.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Xml.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Xml.Linq.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Xml.ReaderWriter.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Xml.Serialization.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Xml.XDocument.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Xml.XmlDocument.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Xml.XmlSerializer.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Xml.XPath.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\System.Xml.XPath.XDocument.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\refs\WindowsBase.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Castle.Core.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.AspNetCore.Mvc.Testing.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.AspNetCore.OpenApi.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.AspNetCore.TestHost.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Data.Sqlite.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.Relational.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.Sqlite.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Caching.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Caching.Memory.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Configuration.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Configuration.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Configuration.Binder.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Configuration.CommandLine.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Configuration.EnvironmentVariables.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Configuration.FileExtensions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Configuration.Json.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Configuration.UserSecrets.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.DependencyInjection.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.DependencyModel.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Diagnostics.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Diagnostics.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.FileProviders.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.FileProviders.Physical.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.FileSystemGlobbing.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Hosting.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Hosting.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Logging.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Logging.Abstractions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Logging.Configuration.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Logging.Console.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Logging.Debug.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Logging.EventLog.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Logging.EventSource.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Options.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Options.ConfigurationExtensions.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.Extensions.Primitives.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Microsoft.OpenApi.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Moq.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\SQLitePCLRaw.batteries_v2.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\SQLitePCLRaw.core.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\SQLitePCLRaw.provider.e_sqlite3.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Swashbuckle.AspNetCore.Swagger.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerGen.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerUI.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\System.Diagnostics.EventLog.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\runtimes\browser-wasm\nativeassets\net9.0\e_sqlite3.a
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\runtimes\linux-arm\native\libe_sqlite3.so
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\runtimes\linux-arm64\native\libe_sqlite3.so
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\runtimes\linux-armel\native\libe_sqlite3.so
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\runtimes\linux-mips64\native\libe_sqlite3.so
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\runtimes\linux-musl-arm\native\libe_sqlite3.so
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\runtimes\linux-musl-arm64\native\libe_sqlite3.so
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\runtimes\linux-musl-s390x\native\libe_sqlite3.so
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\runtimes\linux-musl-x64\native\libe_sqlite3.so
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\runtimes\linux-ppc64le\native\libe_sqlite3.so
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\runtimes\linux-s390x\native\libe_sqlite3.so
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\runtimes\linux-x64\native\libe_sqlite3.so
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\runtimes\linux-x86\native\libe_sqlite3.so
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\runtimes\maccatalyst-arm64\native\libe_sqlite3.dylib
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\runtimes\maccatalyst-x64\native\libe_sqlite3.dylib
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\runtimes\osx-arm64\native\libe_sqlite3.dylib
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\runtimes\osx-x64\native\libe_sqlite3.dylib
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\runtimes\win-arm\native\e_sqlite3.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\runtimes\win-arm64\native\e_sqlite3.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\runtimes\win-x64\native\e_sqlite3.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\runtimes\win-x86\native\e_sqlite3.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.Diagnostics.EventLog.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\todoapi.dll
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\bin\Debug\net9.0\todoapi.pdb
M:\aDev25\vanillaJS\TodoAI25Solution\TestProject\obj\Debug\net9.0\MvcTestingAppManifest.json
