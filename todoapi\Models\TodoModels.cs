namespace todoapi.Models;

public class TodoTask
{
    public string Id { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string? Notes { get; set; }
    public bool IsCompleted { get; set; } = false;
    public bool IsImportant { get; set; } = false;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? CompletedAt { get; set; }
    public DateTime? DueDate { get; set; }
    public string? Recurrence { get; set; }
    public string? MyDayDate { get; set; }
    public string? ListId { get; set; }

    // Navigation properties
    public virtual TodoList? List { get; set; }
    public virtual ICollection<TaskAssignee> Assignees { get; set; } = new List<TaskAssignee>();
}

public class TodoList
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Icon { get; set; } = "??";
    public bool Smart { get; set; } = false;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual ICollection<TodoTask> Tasks { get; set; } = new List<TodoTask>();
}

public class User
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Avatar { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual ICollection<TaskAssignee> TaskAssignments { get; set; } = new List<TaskAssignee>();
}

public class TaskAssignee
{
    public string TaskId { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public DateTime AssignedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual TodoTask Task { get; set; } = null!;
    public virtual User User { get; set; } = null!;
}