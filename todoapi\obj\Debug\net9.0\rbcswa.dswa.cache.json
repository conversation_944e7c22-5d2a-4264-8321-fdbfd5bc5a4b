{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["JMYpEF1tnBWPV44wQ7s3bGxEbSsAGytV2MWQvd5j+wo=", "qrnKepOEbsAVZUn3+gGsnXBfu/ZEU9J1Aa+JdQB23n4="], "CachedAssets": {"qrnKepOEbsAVZUn3+gGsnXBfu/ZEU9J1Aa+JdQB23n4=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\9vecr4ppa8-o7kzmwadhj.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "index9#[.{fingerprint=o7kzmwadhj}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3j2xuvqq0v", "Integrity": "8ZqHjOoNYBO+CDfXncMOnCa/6p75C/Gh01Un++ru/jU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9.html", "FileLength": 18844, "LastWriteTime": "2025-09-28T11:42:02+00:00"}, "JMYpEF1tnBWPV44wQ7s3bGxEbSsAGytV2MWQvd5j+wo=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\odg1uj9emm-5lbvunm6pn.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "index#[.{fingerprint=5lbvunm6pn}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cus234pdx0", "Integrity": "fL6DYJfZe8Kh5PgiL0pbcziLtCpR2DefJfiZd+wDREc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index.html", "FileLength": 765, "LastWriteTime": "2025-09-28T11:42:02+00:00"}}, "CachedCopyCandidates": {}}