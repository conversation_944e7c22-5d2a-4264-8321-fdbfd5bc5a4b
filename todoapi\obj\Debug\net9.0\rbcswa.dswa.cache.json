{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["FNFM64E5pwi0GBe0ZcUpn81VSPqpcWwFJU6s28BPBAU=", "m5MjSTDnDDlaN0fH5uTNJBwqEphbCVThJ+cFuwNc+iE=", "z9kw0d2HQZ7LQ41s6u2Zo8d7Ua8RFoDlQnnlbXOHTtk=", "TYr7B8+vi2ds7j0uOHt2tkJ5D0BA0kV8h3/2uSeK4Pw=", "Yo5dMF0B3/ILSZSttRw8/AU0FqUmn5LJQTa/On7D7p4=", "24U6tZ4oTDUSChvjryPB71Griz6pZTpnQmpBk0nE7ug=", "zNvW2nYlj1RLiQZnFRQxflhCE/LVo5Y48k+IlnuwhbI=", "qSmzHAcMtVVS9pzadUnIt6yxRbyI0gh4eDYUfyys1Mc=", "2ZRlwGPtSwx6hhfHmjJpxNEjz6GC9jkMufon6Wv9j9A=", "4FEeg/q6/zQ71MZbHhC7jj3FNKF4ZcO+PnuU5VhKwtk=", "UYoigMGKPOCRJW7tQvwdNF+hKvzy39EvVwuWdvxTYdg=", "M4SlYJqYb9/EwEeyuvmrLbGbdxONesmQ5Os7klPZCVU="], "CachedAssets": {"M4SlYJqYb9/EwEeyuvmrLbGbdxONesmQ5Os7klPZCVU=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\uvhoxifli3-zhln9tevi0.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "ui-manager#[.{fingerprint=zhln9tevi0}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\ui-manager.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j2j7ym9x2g", "Integrity": "S6Y2dIN39rkBvg2Af8QgOB9BTy/W1v1ebCYkn5vqejM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\ui-manager.js", "FileLength": 7057, "LastWriteTime": "2025-09-28T13:47:44+00:00"}, "m5MjSTDnDDlaN0fH5uTNJBwqEphbCVThJ+cFuwNc+iE=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\a9fs2lewzf-5lbvunm6pn.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "backup/index#[.{fingerprint=5lbvunm6pn}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sfkj51iytr", "Integrity": "OFeln9w3U2lkCl5GtvBgJtU2fh1GaGB0HrJkH+f0wrs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index.html", "FileLength": 760, "LastWriteTime": "2025-09-28T13:44:20+00:00"}, "z9kw0d2HQZ7LQ41s6u2Zo8d7Ua8RFoDlQnnlbXOHTtk=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\qje2162odc-uk5yjv168n.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "backup/index9 - Copy#[.{fingerprint=uk5yjv168n}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index9 - Copy.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j34but2afx", "Integrity": "gQTTL93AbFv+lBWIRxh5OVS81Yz4rVAtF0z9xUM/SMQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index9 - Copy.html", "FileLength": 21251, "LastWriteTime": "2025-09-28T13:44:20+00:00"}, "24U6tZ4oTDUSChvjryPB71Griz6pZTpnQmpBk0nE7ug=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\x6wn2n7ioy-olqgrn1tj4.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "backup/index9#[.{fingerprint=olqgrn1tj4}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index9.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aaxymre4dp", "Integrity": "/iCnh7XGsG6RmEuHAqxqla/uuaAIGz3GvoyD/4MDW08=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index9.html", "FileLength": 3293, "LastWriteTime": "2025-09-28T23:45:52+00:00"}, "Yo5dMF0B3/ILSZSttRw8/AU0FqUmn5LJQTa/On7D7p4=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\3g57br3xf1-q7oz31h1by.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "backup/index9-clean#[.{fingerprint=q7oz31h1by}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index9-clean.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e80im4fdr1", "Integrity": "rmcl4NLapeBROJihhf6i27bvTpoeV8nl1TsyesALXv8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index9-clean.html", "FileLength": 3313, "LastWriteTime": "2025-09-28T23:45:52+00:00"}, "UYoigMGKPOCRJW7tQvwdNF+hKvzy39EvVwuWdvxTYdg=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\me9ah3j1xc-yirdgu4cai.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "todo-app#[.{fingerprint=yirdgu4cai}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\todo-app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d1ivtdx6iu", "Integrity": "73F3jtJKw8iLgSygXmbs0RMmml4rdmMezHI3HpY7jiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\todo-app.js", "FileLength": 4659, "LastWriteTime": "2025-09-28T13:47:44+00:00"}, "4FEeg/q6/zQ71MZbHhC7jj3FNKF4ZcO+PnuU5VhKwtk=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\ae8ekgrttx-mb95wunvkt.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "styles#[.{fingerprint=mb95wunvkt}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5k9yzxuzo1", "Integrity": "5SsbBNT/tJF3BhUIoOeC6Hpd9FQozc56iUv2Qap7q9I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\styles.css", "FileLength": 4360, "LastWriteTime": "2025-09-28T13:44:20+00:00"}, "TYr7B8+vi2ds7j0uOHt2tkJ5D0BA0kV8h3/2uSeK4Pw=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\3rav02kpz0-595uqhdyu2.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "backup/index9-backup#[.{fingerprint=595uqhdyu2}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index9-backup.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f2ppdhzrod", "Integrity": "J0wQBfJ/jM46tUFbkcLrW26DGvVvT9zjpd6hWDQulag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index9-backup.html", "FileLength": 16997, "LastWriteTime": "2025-09-28T23:45:52+00:00"}, "FNFM64E5pwi0GBe0ZcUpn81VSPqpcWwFJU6s28BPBAU=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\hp8lip8dl5-lntny8cd3c.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "app#[.{fingerprint=lntny8cd3c}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ribl8xwmof", "Integrity": "k9kaQHrddU4x76jwtJ+EzHZ9U8cPYjGWB52wnBq4XRA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\app.js", "FileLength": 3581, "LastWriteTime": "2025-09-28T13:47:44+00:00"}, "zNvW2nYlj1RLiQZnFRQxflhCE/LVo5Y48k+IlnuwhbI=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\cdfw8utpt2-5lfc2i8sl7.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "index10#[.{fingerprint=5lfc2i8sl7}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index10.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fro4683dv5", "Integrity": "W6zLe8LlKGYZrXGrS7Ssd8WRqgDFNG1lJX6ENjhgdbQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index10.html", "FileLength": 2729, "LastWriteTime": "2025-09-28T23:45:52+00:00"}, "qSmzHAcMtVVS9pzadUnIt6yxRbyI0gh4eDYUfyys1Mc=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\gk20r1fn6m-3gr8uxwobs.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "simple-app#[.{fingerprint=3gr8uxwobs}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\simple-app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uzvu3x47qc", "Integrity": "egRMIxG9F7VG+0SYVhcDe+cylBYKlM6GPigJvKJcrno=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\simple-app.js", "FileLength": 2514, "LastWriteTime": "2025-09-28T23:45:52+00:00"}, "2ZRlwGPtSwx6hhfHmjJpxNEjz6GC9jkMufon6Wv9j9A=": {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\z4pethen3f-xjex4zorxw.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "simple-ui#[.{fingerprint=xjex4zorxw}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\simple-ui.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6vljcbt52d", "Integrity": "cp1ICAQjIuI6Kn9i06hEofwik2J7EZj/BM4daQRLJmg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\simple-ui.js", "FileLength": 2397, "LastWriteTime": "2025-09-28T23:45:52+00:00"}}, "CachedCopyCandidates": {}}