{"Version": 1, "WorkspaceRootPath": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|m:\\adev25\\vanillajs\\todoai25solution\\todoapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|solutionrelative:todoapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|m:\\adev25\\vanillajs\\todoai25solution\\todoapi\\wwwroot\\index9.html||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|solutionrelative:todoapi\\wwwroot\\index9.html||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|m:\\adev25\\vanillajs\\todoai25solution\\todoapi\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|solutionrelative:todoapi\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|m:\\adev25\\vanillajs\\todoai25solution\\todoapi\\controllers\\userscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|solutionrelative:todoapi\\controllers\\userscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|m:\\adev25\\vanillajs\\todoai25solution\\todoapi\\controllers\\listscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|solutionrelative:todoapi\\controllers\\listscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C311B84B-6408-4CE0-848F-5189E61308B4}|TestProject\\TestProject.csproj|m:\\adev25\\vanillajs\\todoai25solution\\testproject\\unittest1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C311B84B-6408-4CE0-848F-5189E61308B4}|TestProject\\TestProject.csproj|solutionrelative:testproject\\unittest1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8281A189-E8D7-4802-AC36-8C4DBA0E8F86}|Test\\Test.csproj|m:\\adev25\\vanillajs\\todoai25solution\\test\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8281A189-E8D7-4802-AC36-8C4DBA0E8F86}|Test\\Test.csproj|solutionrelative:test\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|m:\\adev25\\vanillajs\\todoai25solution\\todoapi\\readme.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}|", "RelativeMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|solutionrelative:todoapi\\readme.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}|"}, {"AbsoluteMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|m:\\adev25\\vanillajs\\todoai25solution\\todoapi\\data\\tododbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|solutionrelative:todoapi\\data\\tododbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|m:\\adev25\\vanillajs\\todoai25solution\\todoapi\\todoapi.http||{5703B403-55E7-4C63-8C88-A8F52C7A45C5}", "RelativeMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|solutionrelative:todoapi\\todoapi.http||{5703B403-55E7-4C63-8C88-A8F52C7A45C5}"}, {"AbsoluteMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|m:\\adev25\\vanillajs\\todoai25solution\\todoapi\\todoapi.http||{3B902123-F8A7-4915-9F01-361F908088D0}|", "RelativeMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|solutionrelative:todoapi\\todoapi.http||{3B902123-F8A7-4915-9F01-361F908088D0}|"}, {"AbsoluteMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|m:\\adev25\\vanillajs\\todoai25solution\\todoapi\\models\\todomodels.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|solutionrelative:todoapi\\models\\todomodels.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|m:\\adev25\\vanillajs\\todoai25solution\\todoapi\\dtos\\tododtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|solutionrelative:todoapi\\dtos\\tododtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|m:\\adev25\\vanillajs\\todoai25solution\\todoapi\\services\\todoservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|solutionrelative:todoapi\\services\\todoservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|m:\\adev25\\vanillajs\\todoai25solution\\todoapi\\controllers\\taskscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|solutionrelative:todoapi\\controllers\\taskscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|m:\\adev25\\vanillajs\\todoai25solution\\todoapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{2592C8C1-3CD8-4690-92CD-58AE5BE77B64}|todoapi\\todoapi.csproj|solutionrelative:todoapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 7, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}, {"$type": "Document", "DocumentIndex": 13, "Title": "TodoService.cs", "DocumentMoniker": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\Services\\TodoService.cs", "RelativeDocumentMoniker": "todoapi\\Services\\TodoService.cs", "ToolTip": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\Services\\TodoService.cs", "RelativeToolTip": "todoapi\\Services\\TodoService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAANwBAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-28T03:16:31.024Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "TodoDbContext.cs", "DocumentMoniker": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\Data\\TodoDbContext.cs", "RelativeDocumentMoniker": "todoapi\\Data\\TodoDbContext.cs", "ToolTip": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\Data\\TodoDbContext.cs", "RelativeToolTip": "todoapi\\Data\\TodoDbContext.cs", "ViewState": "AgIAAGgAAAAAAAAAAAAuwIsAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-28T03:15:21.589Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "TodoDTOs.cs", "DocumentMoniker": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\DTOs\\TodoDTOs.cs", "RelativeDocumentMoniker": "todoapi\\DTOs\\TodoDTOs.cs", "ToolTip": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\DTOs\\TodoDTOs.cs", "RelativeToolTip": "todoapi\\DTOs\\TodoDTOs.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAF8AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-28T03:15:00.078Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "TodoModels.cs", "DocumentMoniker": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\Models\\TodoModels.cs", "RelativeDocumentMoniker": "todoapi\\Models\\TodoModels.cs", "ToolTip": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\Models\\TodoModels.cs", "RelativeToolTip": "todoapi\\Models\\TodoModels.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAADYAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-28T03:14:39.699Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "index9.html", "DocumentMoniker": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9.html", "RelativeDocumentMoniker": "todoapi\\wwwroot\\index9.html", "ToolTip": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9.html", "RelativeToolTip": "todoapi\\wwwroot\\index9.html", "ViewState": "AgIAAAAAAAAAAAAAAAAYwJABAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001512|", "WhenOpened": "2025-09-28T03:11:08.57Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "Program.cs", "DocumentMoniker": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\Program.cs", "RelativeDocumentMoniker": "todoapi\\Program.cs", "ToolTip": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\Program.cs", "RelativeToolTip": "todoapi\\Program.cs", "ViewState": "AgIAAEUAAAAAAAAAAAAtwEgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-27T19:08:46.075Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "UsersController.cs", "DocumentMoniker": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\Controllers\\UsersController.cs", "RelativeDocumentMoniker": "todoapi\\Controllers\\UsersController.cs", "ToolTip": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\Controllers\\UsersController.cs", "RelativeToolTip": "todoapi\\Controllers\\UsersController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-28T03:17:51.812Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "UnitTest1.cs", "DocumentMoniker": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\TestProject\\UnitTest1.cs", "RelativeDocumentMoniker": "TestProject\\UnitTest1.cs", "ToolTip": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\TestProject\\UnitTest1.cs", "RelativeToolTip": "TestProject\\UnitTest1.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-28T11:41:15.627Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "launchSettings.json", "DocumentMoniker": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "todoapi\\Properties\\launchSettings.json", "ToolTip": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\Properties\\launchSettings.json", "RelativeToolTip": "todoapi\\Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-09-27T19:07:29.511Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "ListsController.cs", "DocumentMoniker": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\Controllers\\ListsController.cs", "RelativeDocumentMoniker": "todoapi\\Controllers\\ListsController.cs", "ToolTip": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\Controllers\\ListsController.cs", "RelativeToolTip": "todoapi\\Controllers\\ListsController.cs", "ViewState": "AgIAABAAAAAAAAAAAAAgwB8AAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-28T03:17:20.429Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "Program.cs", "DocumentMoniker": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\Test\\Program.cs", "RelativeDocumentMoniker": "Test\\Program.cs", "ToolTip": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\Test\\Program.cs", "RelativeToolTip": "Test\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-28T11:40:03.848Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "README.md", "DocumentMoniker": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\README.md", "RelativeDocumentMoniker": "todoapi\\README.md", "ToolTip": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\README.md", "RelativeToolTip": "todoapi\\README.md", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-09-28T03:44:21.239Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "TasksController.cs", "DocumentMoniker": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\Controllers\\TasksController.cs", "RelativeDocumentMoniker": "todoapi\\Controllers\\TasksController.cs", "ToolTip": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\Controllers\\TasksController.cs", "RelativeToolTip": "todoapi\\Controllers\\TasksController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAGwBAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-28T03:16:59.724Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "appsettings.json", "DocumentMoniker": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\appsettings.json", "RelativeDocumentMoniker": "todoapi\\appsettings.json", "ToolTip": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\appsettings.json", "RelativeToolTip": "todoapi\\appsettings.json", "ViewState": "AgIAAAoAAAAAAAAAAABRwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-09-28T03:18:19.439Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "todoapi.http", "DocumentMoniker": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\todoapi.http", "RelativeDocumentMoniker": "todoapi\\todoapi.http", "ToolTip": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\todoapi.http", "RelativeToolTip": "todoapi\\todoapi.http", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003502|", "WhenOpened": "2025-09-27T20:03:21.61Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "todoapi.http", "DocumentMoniker": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\todoapi.http", "RelativeDocumentMoniker": "todoapi\\todoapi.http", "ToolTip": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\todoapi.http", "RelativeToolTip": "todoapi\\todoapi.http", "ViewState": "AgIAAHcAAAAAAAAAAIAwwJIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003502|", "WhenOpened": "2025-09-27T19:09:49.228Z"}]}]}]}