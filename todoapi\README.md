# Todo API - Full Stack Application

A comprehensive Todo application built with ASP.NET Core 9 Web API backend and Vanilla JavaScript frontend, using SQLite database for persistence.

## Architecture

```
Frontend (Vanilla JS)
    ?? HTTP Requests/Responses
Backend (ASP.NET Core 9 Web API)
    ?? Data Operations
Database (SQLite)
```

## Features

### Frontend Features
- ? **Advanced Todo Management**: Create, edit, delete, and organize tasks
- ? **Smart Lists**: All Tasks, My Day, Important, Planned
- ? **Custom Lists**: Create and manage custom task lists
- ? **User Management**: Assign tasks to different users
- ? **Drag & Drop**: Reorder tasks and move between lists
- ? **Search & Filter**: Find tasks quickly with search functionality
- ? **Sort Options**: Multiple sorting criteria for tasks
- ? **Bulk Operations**: Select and operate on multiple tasks
- ? **Dark/Light Theme**: Toggle between themes
- ? **Keyboard Shortcuts**: Efficient keyboard navigation
- ? **Offline Support**: Cache data and sync when online
- ? **Conflict Resolution**: Handle data conflicts intelligently
- ? **Analytics**: Task statistics and insights
- ? **Data Export**: Export to CSV and print functionality
- ? **Responsive Design**: Works on desktop and mobile

### Backend Features
- ? **RESTful API**: Complete CRUD operations for tasks, lists, and users
- ? **Entity Framework Core**: ORM with SQLite database
- ? **Swagger Documentation**: Interactive API documentation
- ? **CORS Support**: Enable frontend-backend communication
- ? **Error Handling**: Comprehensive error responses
- ? **Seed Data**: Pre-populated with sample data
- ? **Bulk Operations**: Efficient multi-task operations
- ? **Statistics API**: Get task analytics and insights

## Project Structure

```
todoapi/
??? Controllers/           # API Controllers
?   ??? TasksController.cs    # Task management endpoints
?   ??? ListsController.cs    # List management endpoints
?   ??? UsersController.cs    # User management endpoints
??? Data/
?   ??? TodoDbContext.cs      # Entity Framework DbContext
??? DTOs/                  # Data Transfer Objects
?   ??? TodoDTOs.cs          # API request/response models
??? Models/               # Database models
?   ??? TodoModels.cs        # Entity models
??? Services/             # Business logic layer
?   ??? TodoService.cs       # Core business logic
??? wwwroot/              # Static files
?   ??? index.html          # Simple welcome page
?   ??? index9.html         # Full Todo application
??? Properties/
?   ??? launchSettings.json  # App launch configuration
??? Program.cs             # Application entry point
??? appsettings.json       # Configuration settings
??? todoapi.csproj         # Project file
??? todoapi.http           # HTTP test requests
??? todoapp.db            # SQLite database (generated)
```

## API Endpoints

### Tasks
- `GET /api/tasks` - Get all tasks
- `POST /api/tasks` - Create a new task
- `GET /api/tasks/{id}` - Get task by ID
- `PUT /api/tasks/{id}` - Update a task
- `DELETE /api/tasks/{id}` - Delete a task
- `POST /api/tasks/bulk` - Bulk operations (complete, delete, important)
- `GET /api/tasks/by-list/{listId}` - Get tasks by list
- `GET /api/tasks/by-user/{userId}` - Get tasks by user
- `GET /api/tasks/important` - Get important tasks
- `GET /api/tasks/my-day/{date}` - Get my day tasks
- `GET /api/tasks/planned` - Get planned tasks (with due dates)

### Lists
- `GET /api/lists` - Get all lists
- `POST /api/lists` - Create a new list
- `GET /api/lists/{id}` - Get list by ID
- `PUT /api/lists/{id}` - Update a list
- `DELETE /api/lists/{id}` - Delete a list

### Users
- `GET /api/users` - Get all users
- `POST /api/users` - Create a new user
- `GET /api/users/{id}` - Get user by ID
- `PUT /api/users/{id}` - Update a user
- `DELETE /api/users/{id}` - Delete a user

### Statistics
- `GET /api/stats` - Get application statistics

## Getting Started

### Prerequisites
- .NET 9.0 SDK
- Visual Studio 2022 or VS Code

### Running the Application

1. **Clone and navigate to the project**
   ```bash
   cd todoapi
   ```

2. **Restore packages**
   ```bash
   dotnet restore
   ```

3. **Run the application**
   ```bash
   dotnet run
   ```

4. **Access the application**
   - Frontend: http://localhost:5000/index9.html
   - Swagger UI: http://localhost:5000/swagger
   - Simple welcome: http://localhost:5000/

### Testing the API

Use the provided `todoapi.http` file with your HTTP client:
- Visual Studio Code: Install REST Client extension
- Visual Studio: Built-in HTTP client support
- Or use any API testing tool like Postman

### Database

The application uses SQLite with the following features:
- **Automatic creation**: Database is created automatically on first run
- **Seed data**: Pre-populated with sample tasks, lists, and users
- **Location**: `todoapp.db` in the project root
- **EF Core**: Entity Framework Core for data access

## Sample Data

The application comes with pre-seeded data:

### Users
- Dad, Mom, Jr. John, Mike, Louie (with different avatar colors)

### Lists
- ?? Grocery List
- ?? Work Tasks  
- ?? Personal

### Tasks
- Sample tasks assigned to different users and lists
- Mix of completed/incomplete, important, with due dates

## Key Technologies

- **Backend**: ASP.NET Core 9.0, Entity Framework Core, SQLite
- **Frontend**: Vanilla JavaScript (ES6+), HTML5, CSS3
- **API Documentation**: Swagger/OpenAPI
- **Database**: SQLite with EF Core Code First
- **Styling**: CSS Custom Properties, Flexbox, Grid
- **Offline Support**: LocalStorage caching with conflict resolution

## Development Notes

### Frontend Architecture
- **Service Layer**: Real API calls with offline fallback
- **State Management**: Centralized application state
- **UI Components**: Modular UI management class
- **Conflict Resolution**: Automatic and manual conflict handling
- **Caching**: Intelligent caching with versioning

### Backend Architecture
- **Repository Pattern**: Service layer abstraction
- **DTO Pattern**: Separate API models from database models
- **Error Handling**: Consistent API response format
- **Logging**: Structured logging with Entity Framework
- **CORS**: Configured for frontend integration

## Future Enhancements

- [ ] Authentication and authorization
- [ ] Real-time updates with SignalR
- [ ] File attachments for tasks
- [ ] Email notifications
- [ ] Calendar integration
- [ ] Mobile app with shared API
- [ ] Advanced reporting and analytics
- [ ] Task templates and recurring tasks
- [ ] Team collaboration features
- [ ] Integration with external services

## License

This project is for educational and demonstration purposes.