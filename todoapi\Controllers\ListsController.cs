using Microsoft.AspNetCore.Mvc;
using todoapi.DTOs;
using todoapi.Services;

namespace todoapi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ListsController : ControllerBase
{
    private readonly ITodoService _todoService;
    private readonly ILogger<ListsController> _logger;

    public ListsController(ITodoService todoService, ILogger<ListsController> logger)
    {
        _todoService = todoService;
        _logger = logger;
    }

    /// <summary>
    /// Get all lists
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<IEnumerable<TodoListDto>>>> GetAllLists()
    {
        try
        {
            var lists = await _todoService.GetAllListsAsync();
            return Ok(new ApiResponse<IEnumerable<TodoListDto>>
            {
                Success = true,
                Data = lists,
                Message = "Lists retrieved successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all lists");
            return StatusCode(500, new ApiResponse<IEnumerable<TodoListDto>>
            {
                Success = false,
                Message = "Internal server error",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Get list by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<ApiResponse<TodoListDto>>> GetList(string id)
    {
        try
        {
            var list = await _todoService.GetListByIdAsync(id);
            if (list == null)
            {
                return NotFound(new ApiResponse<TodoListDto>
                {
                    Success = false,
                    Message = "List not found"
                });
            }

            return Ok(new ApiResponse<TodoListDto>
            {
                Success = true,
                Data = list,
                Message = "List retrieved successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting list {ListId}", id);
            return StatusCode(500, new ApiResponse<TodoListDto>
            {
                Success = false,
                Message = "Internal server error",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Create a new list
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<ApiResponse<TodoListDto>>> CreateList([FromBody] CreateTodoListDto listDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new ApiResponse<TodoListDto>
                {
                    Success = false,
                    Message = "Validation failed",
                    Errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList()
                });
            }

            var createdList = await _todoService.CreateListAsync(listDto);
            return CreatedAtAction(nameof(GetList), new { id = createdList.Id }, 
                new ApiResponse<TodoListDto>
                {
                    Success = true,
                    Data = createdList,
                    Message = "List created successfully"
                });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating list");
            return StatusCode(500, new ApiResponse<TodoListDto>
            {
                Success = false,
                Message = "Internal server error",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Update an existing list
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<ApiResponse<TodoListDto>>> UpdateList(string id, [FromBody] CreateTodoListDto listDto)
    {
        try
        {
            var updatedList = await _todoService.UpdateListAsync(id, listDto);
            if (updatedList == null)
            {
                return NotFound(new ApiResponse<TodoListDto>
                {
                    Success = false,
                    Message = "List not found"
                });
            }

            return Ok(new ApiResponse<TodoListDto>
            {
                Success = true,
                Data = updatedList,
                Message = "List updated successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating list {ListId}", id);
            return StatusCode(500, new ApiResponse<TodoListDto>
            {
                Success = false,
                Message = "Internal server error",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Delete a list
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult<ApiResponse<bool>>> DeleteList(string id)
    {
        try
        {
            var result = await _todoService.DeleteListAsync(id);
            if (!result)
            {
                return NotFound(new ApiResponse<bool>
                {
                    Success = false,
                    Message = "List not found"
                });
            }

            return Ok(new ApiResponse<bool>
            {
                Success = true,
                Data = true,
                Message = "List deleted successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting list {ListId}", id);
            return StatusCode(500, new ApiResponse<bool>
            {
                Success = false,
                Message = "Internal server error",
                Errors = { ex.Message }
            });
        }
    }
}