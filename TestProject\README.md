# UsersController Tests

This project contains comprehensive unit tests for the `UsersController` in the Todo API.

## Test Coverage

The `UsersControllerTests` class covers all public methods of the `UsersController`:

### GetAllUsers Tests
- ✅ Returns OK result with users when users exist
- ✅ Returns empty list when no users exist
- ✅ Returns internal server error when exception is thrown

### GetUser Tests
- ✅ Returns OK result when user exists
- ✅ Returns NotFound when user doesn't exist
- ✅ Returns internal server error when exception is thrown

### CreateUser Tests
- ✅ Returns Created result with valid user data
- ✅ Returns BadRequest when model state is invalid
- ✅ Returns internal server error when exception is thrown

### UpdateUser Tests
- ✅ Returns OK result when user is updated successfully
- ✅ Returns NotFound when user doesn't exist
- ✅ Returns internal server error when exception is thrown

### DeleteUser Tests
- ✅ Returns OK result when user is deleted successfully
- ✅ Returns NotFound when user doesn't exist
- ✅ Returns internal server error when exception is thrown

## Dependencies

The tests use the following packages:
- **xUnit** - Testing framework
- **Moq** - Mocking framework for dependencies
- **Microsoft.AspNetCore.Mvc.Testing** - ASP.NET Core testing utilities

## Running Tests

### Command Line
```bash
dotnet test
```

### PowerShell Script
```powershell
.\run-tests.ps1
```

### Visual Studio
- Open Test Explorer
- Run all tests or specific test methods

## Test Structure

Each test follows the **Arrange-Act-Assert** pattern:
- **Arrange**: Set up test data and mock dependencies
- **Act**: Execute the method under test
- **Assert**: Verify the expected results

## Mock Dependencies

The tests mock the following dependencies:
- `ITodoService` - Business logic service
- `ILogger<UsersController>` - Logging service

This ensures tests are isolated and don't depend on external systems like databases.
