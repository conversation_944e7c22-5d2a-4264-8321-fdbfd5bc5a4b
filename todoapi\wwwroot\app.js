// Cache Service for offline functionality with conflict detection
class CacheService {
    constructor() {
        this.prefix = 'todoapp_';
    }

    set(key, value) {
        try {
            const versionedData = {
                data: value,
                version: Date.now(),
                lastModified: new Date().toISOString(),
                checksum: this.generateChecksum(value)
            };
            localStorage.setItem(this.prefix + key, JSON.stringify(versionedData));
            console.log(`💾 Cached ${key}:`, Array.isArray(value) ? `${value.length} items` : typeof value);
        } catch (e) {
            console.warn('Failed to save to localStorage:', e);
        }
    }

    get(key) {
        try {
            const item = localStorage.getItem(this.prefix + key);
            if (!item) return null;

            const parsed = JSON.parse(item);
            // Handle legacy data without versioning
            if (parsed.data !== undefined) {
                return parsed.data;
            }
            return parsed; // Legacy format
        } catch (e) {
            console.warn('Failed to read from localStorage:', e);
            return null;
        }
    }

    getVersioned(key) {
        try {
            const item = localStorage.getItem(this.prefix + key);
            if (!item) return null;

            const parsed = JSON.parse(item);
            // Handle legacy data without versioning
            if (parsed.data === undefined) {
                return {
                    data: parsed,
                    version: 0,
                    lastModified: new Date().toISOString(),
                    checksum: this.generateChecksum(parsed)
                };
            }
            return parsed;
        } catch (e) {
            console.warn('Failed to read versioned data from localStorage:', e);
            return null;
        }
    }

    remove(key) {
        try {
            localStorage.removeItem(this.prefix + key);
        } catch (e) {
            console.warn('Failed to remove from localStorage:', e);
        }
    }

    generateChecksum(data) {
        // Simple checksum for conflict detection
        const str = JSON.stringify(data);
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString();
    }

    detectConflict(key, serverData) {
        const localVersioned = this.getVersioned(key);
        if (!localVersioned) return null;

        const serverChecksum = this.generateChecksum(serverData);
        const localChecksum = localVersioned.checksum;

        if (serverChecksum !== localChecksum) {
            return {
                type: 'data_conflict',
                key,
                local: localVersioned,
                server: {
                    data: serverData,
                    version: Date.now(),
                    lastModified: new Date().toISOString(),
                    checksum: serverChecksum
                }
            };
        }
        return null;
    }

    queueSync(action, id, data = null) {
        const pending = this.get('pendingSync') || [];
        pending.push({
            action,
            id,
            data,
            timestamp: Date.now(),
            version: Date.now(),
            retryCount: 0
        });
        this.set('pendingSync', pending);
    }

    getConflicts() {
        return this.get('conflicts') || [];
    }

    addConflict(conflict) {
        const conflicts = this.getConflicts();
        conflicts.push({
            ...conflict,
            id: `conflict-${Date.now()}`,
            timestamp: Date.now(),
            status: 'pending'
        });
        this.set('conflicts', conflicts);
    }

    resolveConflict(conflictId, resolution) {
        const conflicts = this.getConflicts();
        const conflict = conflicts.find(c => c.id === conflictId);
        if (conflict) {
            conflict.status = 'resolved';
            conflict.resolution = resolution;
            conflict.resolvedAt = Date.now();
            this.set('conflicts', conflicts);
        }
    }

    removeConflict(conflictId) {
        const conflicts = this.getConflicts();
        const filtered = conflicts.filter(c => c.id !== conflictId);
        this.set('conflicts', filtered);
    }
}

// Conflict Resolution Service
class ConflictResolver {
    constructor(cache) {
        this.cache = cache;
    }

    // Automatic resolution strategies
    resolveConflictAuto(conflict, strategy = 'last-write-wins') {
        switch (strategy) {
            case 'last-write-wins':
                return this.lastWriteWins(conflict);
            case 'server-wins':
                return this.serverWins(conflict);
            case 'local-wins':
                return this.localWins(conflict);
            case 'merge':
                return this.mergeData(conflict);
            default:
                return this.lastWriteWins(conflict);
        }
    }

    lastWriteWins(conflict) {
        const localTime = new Date(conflict.local.lastModified).getTime();
        const serverTime = new Date(conflict.server.lastModified).getTime();

        if (serverTime > localTime) {
            return {
                resolution: 'server-wins',
                data: conflict.server.data,
                reason: 'Server data is more recent'
            };
        } else {
            return {
                resolution: 'local-wins',
                data: conflict.local.data,
                reason: 'Local data is more recent'
            };
        }
    }

    serverWins(conflict) {
        return {
            resolution: 'server-wins',
            data: conflict.server.data,
            reason: 'Server data takes precedence'
        };
    }

    localWins(conflict) {
        return {
            resolution: 'local-wins',
            data: conflict.local.data,
            reason: 'Local data takes precedence'
        };
    }

    mergeData(conflict) {
        const localData = conflict.local.data;
        const serverData = conflict.server.data;

        // Handle different data types
        if (Array.isArray(localData) && Array.isArray(serverData)) {
            return this.mergeArrays(conflict, localData, serverData);
        } else if (typeof localData === 'object' && typeof serverData === 'object') {
            return this.mergeObjects(conflict, localData, serverData);
        } else {
            // For primitive types, fall back to last-write-wins
            return this.lastWriteWins(conflict);
        }
    }

    mergeArrays(conflict, localArray, serverArray) {
        // For tasks/lists arrays, merge by ID and keep most recent versions
        const merged = [];
        const localMap = new Map();
        const serverMap = new Map();

        // Build maps for efficient lookup
        localArray.forEach(item => {
            if (item.id) localMap.set(item.id, item);
        });
        serverArray.forEach(item => {
            if (item.id) serverMap.set(item.id, item);
        });

        // Merge items
        const allIds = new Set([...localMap.keys(), ...serverMap.keys()]);

        allIds.forEach(id => {
            const localItem = localMap.get(id);
            const serverItem = serverMap.get(id);

            if (localItem && serverItem) {
                // Both exist, choose most recent or merge
                const localTime = new Date(localItem.createdAt || localItem.lastModified || 0).getTime();
                const serverTime = new Date(serverItem.createdAt || serverItem.lastModified || 0).getTime();
                merged.push(serverTime > localTime ? serverItem : localItem);
            } else if (localItem) {
                merged.push(localItem);
            } else if (serverItem) {
                merged.push(serverItem);
            }
        });

        return {
            resolution: 'merged',
            data: merged,
            reason: 'Arrays merged by ID with most recent versions'
        };
    }

    mergeObjects(conflict, localObj, serverObj) {
        const merged = { ...localObj };

        // Merge server properties, preferring server for conflicts
        Object.keys(serverObj).forEach(key => {
            if (key === 'lastModified' || key === 'version') {
                // Keep server metadata
                merged[key] = serverObj[key];
            } else if (merged[key] !== serverObj[key]) {
                // For conflicts, prefer server data
                merged[key] = serverObj[key];
            }
        });

        return {
            resolution: 'merged',
            data: merged,
            reason: 'Objects merged with server data taking precedence for conflicts'
        };
    }

    // Get user-friendly conflict description
    getConflictDescription(conflict) {
        const descriptions = {
            'tasks': 'Your tasks have been modified on another device',
            'lists': 'Your custom lists have been modified on another device',
            'users': 'User assignments have been modified on another device',
            'theme': 'Theme preferences have been modified on another device',
            'sortBy': 'Sort preferences have been modified on another device'
        };

        return descriptions[conflict.key] || `Data conflict detected for ${conflict.key}`;
    }

    // Get recommended resolution strategy
    getRecommendedStrategy(conflict) {
        const strategies = {
            'tasks': 'merge',
            'lists': 'merge',
            'users': 'server-wins',
            'theme': 'local-wins',
            'sortBy': 'local-wins'
        };

        return strategies[conflict.key] || 'last-write-wins';
    }

    // Detect conflicts between local and server data
    detectConflicts(localData, serverData) {
        const conflicts = [];
        
        // Check each data type for conflicts
        Object.keys(localData).forEach(key => {
            if (serverData[key] !== undefined) {
                const localVersioned = this.cache.getVersioned(key);
                if (localVersioned) {
                    const serverChecksum = this.cache.generateChecksum(serverData[key]);
                    const localChecksum = localVersioned.checksum;
                    
                    if (serverChecksum !== localChecksum) {
                        conflicts.push({
                            type: 'data_conflict',
                            key,
                            local: localVersioned,
                            server: {
                                data: serverData[key],
                                version: Date.now(),
                                lastModified: new Date().toISOString(),
                                checksum: serverChecksum
                            }
                        });
                    }
                }
            }
        });
        
        return conflicts;
    }
}

// Real API Service for backend communication
class ApiService {
    constructor() {
        this.baseUrl = '/api'; // Use relative URL to work with the same domain
        this.cache = new CacheService();
    }

    async request(endpoint, options = {}) {
        try {
            const url = `${this.baseUrl}${endpoint}`;
            console.log(`🌐 API Request: ${options.method || 'GET'} ${url}`);
            
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });

            console.log(`📡 Response: ${response.status} ${response.statusText}`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
            }

            const data = await response.json();
            console.log(`📦 Response data:`, data);
            
            // Handle API response format
            if (data.success === false) {
                throw new Error(data.message || 'API request failed');
            }

            return data.data || data;
        } catch (error) {
            console.error(`❌ API request failed: ${endpoint}`, error);
            throw error;
        }
    }

    async getTasks() {
        return await this.request('/tasks');
    }

    async getTask(id) {
        return await this.request(`/tasks/${id}`);
    }

    async createTask(taskData) {
        return await this.request('/tasks', {
            method: 'POST',
            body: JSON.stringify({
                title: taskData.title,
                notes: taskData.notes || null,
                dueDate: taskData.dueDate || null,
                recurrence: taskData.recurrence || null,
                isImportant: taskData.isImportant || false,
                myDayDate: taskData.myDayDate || null,
                listId: taskData.listId || null,
                assigneeIds: taskData.assignees?.map(a => a.id) || []
            })
        });
    }

    async updateTask(taskId, updates) {
        return await this.request(`/tasks/${taskId}`, {
            method: 'PUT',
            body: JSON.stringify({
                title: updates.title,
                notes: updates.notes,
                isCompleted: updates.isCompleted,
                isImportant: updates.isImportant,
                dueDate: updates.dueDate,
                recurrence: updates.recurrence,
                myDayDate: updates.myDayDate,
                listId: updates.listId,
                assigneeIds: updates.assignees?.map(a => a.id)
            })
        });
    }

    async deleteTask(taskId) {
        return await this.request(`/tasks/${taskId}`, {
            method: 'DELETE'
        });
    }

    async bulkOperation(taskIds, operation) {
        return await this.request('/tasks/bulk', {
            method: 'POST',
            body: JSON.stringify({
                taskIds,
                operation
            })
        });
    }

    async getTasksByList(listId) {
        return await this.request(`/tasks/by-list/${listId}`);
    }

    async getTasksByUser(userId) {
        return await this.request(`/tasks/by-user/${userId}`);
    }

    async getImportantTasks() {
        return await this.request('/tasks/important');
    }

    async getMyDayTasks(date) {
        return await this.request(`/tasks/my-day/${date}`);
    }

    async getPlannedTasks() {
        return await this.request('/tasks/planned');
    }

    async getLists() {
        return await this.request('/lists');
    }

    async createList(listData) {
        console.log('ApiService.createList called with:', listData);
        const requestData = {
            name: listData.name,
            icon: listData.icon || '📁'
        };
        console.log('Sending request to /lists with data:', requestData);
        const result = await this.request('/lists', {
            method: 'POST',
            body: JSON.stringify(requestData)
        });
        console.log('ApiService.createList result:', result);
        return result;
    }

    async updateList(listId, listData) {
        return await this.request(`/lists/${listId}`, {
            method: 'PUT',
            body: JSON.stringify({
                name: listData.name,
                icon: listData.icon
            })
        });
    }

    async deleteList(listId) {
        return await this.request(`/lists/${listId}`, {
            method: 'DELETE'
        });
    }

    async getUsers() {
        return await this.request('/users');
    }

    async createUser(userData) {
        return await this.request('/users', {
            method: 'POST',
            body: JSON.stringify(userData)
        });
    }

    async updateUser(userId, userData) {
        return await this.request(`/users/${userId}`, {
            method: 'PUT',
            body: JSON.stringify(userData)
        });
    }

    async deleteUser(userId) {
        return await this.request(`/users/${userId}`, {
            method: 'DELETE'
        });
    }

    async getStats() {
        return await this.request('/stats');
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
