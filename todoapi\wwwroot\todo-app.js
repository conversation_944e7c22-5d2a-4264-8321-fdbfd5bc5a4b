// Main Todo Application Class
class TodoApp {
    constructor() {
        this.api = new ApiService();
        this.cache = new CacheService();
        this.conflictResolver = new ConflictResolver(this.cache);
        this.state = {
            tasks: [],
            lists: [],
            users: [],
            theme: this.cache.get('theme') || 'light',
            searchTerm: '',
            sortBy: this.cache.get('sortBy') || 'created-desc',
            currentList: this.cache.get('currentList') || 'all'
        };
        this.ui = new UIManager(this);
        this.autoResolveStrategies = this.cache.get('autoResolveStrategies') || {};
        this.syncInProgress = false;
    }

    async init() {
        console.log('🚀 Initializing Todo App...');
        
        // Set up theme
        document.documentElement.setAttribute('data-theme', this.state.theme);
        
        // Load cached data first for immediate UI
        this.state.tasks = this.cache.get('tasks') || [];
        this.state.lists = this.cache.get('lists') || [];
        this.state.users = this.cache.get('users') || [];
        
        // Render initial UI
        this.ui.render();
        
        try {
            // Try to sync with server
            await this.syncWithServer();
        } catch (error) {
            console.warn('⚠️ Server sync failed, using cached data:', error);
            
            // Fall back to cached data
            this.state.tasks = this.cache.get('tasks') || [];
            this.state.lists = this.cache.get('lists') || [];
            this.state.users = this.cache.get('users') || [];
            
            this.ui.updateSyncStatus('offline');
        }
        
        // Set up periodic sync
        setInterval(() => this.syncWithServer(), 30000); // Sync every 30 seconds
        
        console.log('✅ Todo App initialized successfully');
    }

    async syncWithServer() {
        if (this.syncInProgress) return;
        this.syncInProgress = true;
        
        try {
            this.ui.updateSyncStatus('syncing');
            
            // Fetch fresh data from server
            const [tasks, lists, users] = await Promise.all([
                this.api.getTasks(),
                this.api.getLists(),
                this.api.getUsers()
            ]);
            
            // Check for conflicts
            const conflicts = this.conflictResolver.detectConflicts(
                { tasks: this.state.tasks, lists: this.state.lists, users: this.state.users },
                { tasks, lists, users }
            );
            
            if (conflicts.length > 0) {
                console.log('⚠️ Conflicts detected:', conflicts);
                this.cache.set('conflicts', conflicts);
                this.ui.updateSyncStatus('conflict');
                return;
            }
            
            // Update state with server data
            this.state.tasks = tasks;
            this.state.lists = lists;
            this.state.users = users;
            
            // Update cache
            this.cache.set('tasks', tasks);
            this.cache.set('lists', lists);
            this.cache.set('users', users);
            
            // Re-render UI
            this.ui.render();
            this.ui.updateSyncStatus('online');
            
        } catch (error) {
            console.error('❌ Sync failed:', error);
            this.ui.updateSyncStatus('offline');
        } finally {
            this.syncInProgress = false;
        }
    }

    // Task operations
    async createTask(taskData) {
        try {
            const newTask = await this.api.createTask(taskData);
            this.state.tasks.push(newTask);
            this.cache.set('tasks', this.state.tasks);
            this.ui.render();
            return newTask;
        } catch (error) {
            console.error('Failed to create task:', error);
            throw error;
        }
    }

    async updateTask(taskId, updates) {
        try {
            const updatedTask = await this.api.updateTask(taskId, updates);
            const index = this.state.tasks.findIndex(t => t.id === taskId);
            if (index !== -1) {
                this.state.tasks[index] = updatedTask;
                this.cache.set('tasks', this.state.tasks);
                this.ui.render();
            }
            return updatedTask;
        } catch (error) {
            console.error('Failed to update task:', error);
            throw error;
        }
    }

    async deleteTask(taskId, skipUI = false) {
        try {
            await this.api.deleteTask(taskId);
            this.state.tasks = this.state.tasks.filter(t => t.id !== taskId);
            this.cache.set('tasks', this.state.tasks);
            if (!skipUI) this.ui.render();
        } catch (error) {
            console.error('Failed to delete task:', error);
            throw error;
        }
    }

    async toggleTaskCompletion(taskId, forceComplete = null) {
        const task = this.state.tasks.find(t => t.id === taskId);
        if (!task) return;
        
        const newCompleted = forceComplete !== null ? forceComplete : !task.isCompleted;
        await this.updateTask(taskId, { isCompleted: newCompleted });
    }

    async toggleTaskImportance(taskId) {
        const task = this.state.tasks.find(t => t.id === taskId);
        if (!task) return;
        
        await this.updateTask(taskId, { isImportant: !task.isImportant });
    }

    async duplicateTask(taskId) {
        const task = this.state.tasks.find(t => t.id === taskId);
        if (!task) return;
        
        const duplicatedTask = {
            ...task,
            title: `${task.title} (Copy)`,
            isCompleted: false,
            createdAt: new Date().toISOString(),
            completedAt: null
        };
        
        // Remove the id so a new one gets generated
        delete duplicatedTask.id;
        
        await this.createTask(duplicatedTask);
    }

    // List operations
    async createList(listData) {
        try {
            console.log('TodoApp.createList called with:', listData);
            const newList = await this.api.createList(listData);
            console.log('API returned new list:', newList);
            this.state.lists.push(newList);
            this.cache.set('lists', this.state.lists);
            this.ui.render();
            console.log('List added to state and UI rendered');
            return newList;
        } catch (error) {
            console.error('Failed to create list:', error);
            throw error;
        }
    }

    async updateList(listId, listData) {
        try {
            const updatedList = await this.api.updateList(listId, listData);
            const index = this.state.lists.findIndex(l => l.id === listId);
            if (index !== -1) {
                this.state.lists[index] = updatedList;
                this.cache.set('lists', this.state.lists);
                this.ui.render();
            }
            return updatedList;
        } catch (error) {
            console.error('Failed to update list:', error);
            throw error;
        }
    }

    async deleteList(listId) {
        try {
            await this.api.deleteList(listId);
            this.state.lists = this.state.lists.filter(l => l.id !== listId);
            this.cache.set('lists', this.state.lists);
            this.ui.render();
        } catch (error) {
            console.error('Failed to delete list:', error);
            throw error;
        }
    }

    // User operations
    async createUser(userData) {
        try {
            const newUser = await this.api.createUser(userData);
            this.state.users.push(newUser);
            this.cache.set('users', this.state.users);
            this.ui.render();
            return newUser;
        } catch (error) {
            console.error('Failed to create user:', error);
            throw error;
        }
    }

    async updateUser(userId, userData) {
        try {
            const updatedUser = await this.api.updateUser(userId, userData);
            const index = this.state.users.findIndex(u => u.id === userId);
            if (index !== -1) {
                this.state.users[index] = updatedUser;
                this.cache.set('users', this.state.users);
                this.ui.render();
            }
            return updatedUser;
        } catch (error) {
            console.error('Failed to update user:', error);
            throw error;
        }
    }

    async deleteUser(userId) {
        try {
            await this.api.deleteUser(userId);
            this.state.users = this.state.users.filter(u => u.id !== userId);
            this.cache.set('users', this.state.users);
            this.ui.render();
        } catch (error) {
            console.error('Failed to delete user:', error);
            throw error;
        }
    }

    // Utility methods
    moveTaskToList(taskId, listId) {
        const task = this.state.tasks.find(t => t.id === taskId);
        if (task) {
            task.listId = listId;
            this.updateTask(taskId, { listId });
        }
    }

    reorderTasks(taskId, targetTaskId) {
        // Simple reordering - move task to position of target task
        const tasks = this.state.tasks;
        const taskIndex = tasks.findIndex(t => t.id === taskId);
        const targetIndex = tasks.findIndex(t => t.id === targetTaskId);
        
        if (taskIndex !== -1 && targetIndex !== -1) {
            const [task] = tasks.splice(taskIndex, 1);
            tasks.splice(targetIndex, 0, task);
            this.cache.set('tasks', tasks);
            this.ui.render();
        }
    }

    showNextConflict() {
        const conflicts = this.cache.getConflicts();
        if (conflicts.length > 0) {
            this.ui.showConflictModal(conflicts[0]);
        }
    }

    // Data management
    saveData() {
        const data = {
            tasks: this.state.tasks,
            lists: this.state.lists,
            users: this.state.users,
            timestamp: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `todo-backup-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    loadDataFromFile() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const data = JSON.parse(e.target.result);
                        this.state.tasks = data.tasks || [];
                        this.state.lists = data.lists || [];
                        this.state.users = data.users || [];
                        this.cache.set('tasks', this.state.tasks);
                        this.cache.set('lists', this.state.lists);
                        this.cache.set('users', this.state.users);
                        this.ui.render();
                    } catch (error) {
                        alert('Invalid file format');
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }

    exportCsv() {
        const csv = this.generateCsv();
        const blob = new Blob([csv], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `todo-export-${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        URL.revokeObjectURL(url);
    }

    generateCsv() {
        const headers = ['Title', 'Notes', 'Completed', 'Important', 'Due Date', 'List', 'Assignees'];
        const rows = this.state.tasks.map(task => [
            task.title,
            task.notes || '',
            task.isCompleted ? 'Yes' : 'No',
            task.isImportant ? 'Yes' : 'No',
            task.dueDate || '',
            task.listId ? this.state.lists.find(l => l.id === task.listId)?.name || '' : '',
            task.assignees?.map(a => a.name).join(', ') || ''
        ]);
        
        return [headers, ...rows].map(row => 
            row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',')
        ).join('\n');
    }

    printTasks() {
        const printWindow = window.open('', '_blank');
        const html = `
            <html>
                <head><title>Todo List</title></head>
                <body>
                    <h1>Todo List - ${new Date().toLocaleDateString()}</h1>
                    ${this.state.tasks.map(task => `
                        <div style="margin: 10px 0; padding: 10px; border: 1px solid #ccc;">
                            <strong>${task.title}</strong>
                            ${task.notes ? `<br><em>${task.notes}</em>` : ''}
                            <br>Status: ${task.isCompleted ? 'Completed' : 'Pending'}
                            ${task.isImportant ? '<br><strong>Important</strong>' : ''}
                        </div>
                    `).join('')}
                </body>
            </html>
        `;
        printWindow.document.write(html);
        printWindow.document.close();
        printWindow.print();
    }

    clearCurrentTasks() {
        if (confirm('Are you sure you want to clear all tasks?')) {
            this.state.tasks = [];
            this.cache.set('tasks', []);
            this.ui.render();
        }
    }

    // Filtering and sorting methods
    getFilteredTasks() {
        let tasks = [...this.state.tasks];
        
        // Apply search filter
        if (this.state.searchTerm) {
            const searchLower = this.state.searchTerm.toLowerCase();
            tasks = tasks.filter(task => 
                task.title.toLowerCase().includes(searchLower) ||
                (task.notes && task.notes.toLowerCase().includes(searchLower))
            );
        }
        
        // Apply current list filter
        if (this.state.currentList) {
            if (this.state.currentList === 'my-day') {
                const today = new Date().toISOString().split('T')[0];
                tasks = tasks.filter(task => task.myDayDate === today);
            } else if (this.state.currentList === 'important') {
                tasks = tasks.filter(task => task.isImportant);
            } else if (this.state.currentList === 'planned') {
                tasks = tasks.filter(task => task.dueDate);
            } else if (this.state.currentList.startsWith('user-')) {
                const userId = this.state.currentList.replace('user-', '');
                tasks = tasks.filter(task => 
                    task.assignees && task.assignees.some(a => a.id === userId)
                );
            } else if (this.state.currentList !== 'all') {
                tasks = tasks.filter(task => task.listId === this.state.currentList);
            }
        }
        
        // Apply sorting
        this.sortTasks(tasks);
        
        return tasks;
    }

    sortTasks(tasks) {
        switch (this.state.sortBy) {
            case 'title-asc':
                tasks.sort((a, b) => a.title.localeCompare(b.title));
                break;
            case 'title-desc':
                tasks.sort((a, b) => b.title.localeCompare(a.title));
                break;
            case 'due-date-asc':
                tasks.sort((a, b) => {
                    if (!a.dueDate && !b.dueDate) return 0;
                    if (!a.dueDate) return 1;
                    if (!b.dueDate) return -1;
                    return new Date(a.dueDate) - new Date(b.dueDate);
                });
                break;
            case 'due-date-desc':
                tasks.sort((a, b) => {
                    if (!a.dueDate && !b.dueDate) return 0;
                    if (!a.dueDate) return -1;
                    if (!b.dueDate) return 1;
                    return new Date(b.dueDate) - new Date(a.dueDate);
                });
                break;
            case 'created-asc':
                tasks.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
                break;
            case 'created-desc':
            default:
                tasks.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
                break;
        }
    }

    getTaskStats() {
        const tasks = this.state.tasks;
        const today = new Date().toISOString().split('T')[0];
        
        return {
            total: tasks.length,
            completed: tasks.filter(t => t.isCompleted).length,
            important: tasks.filter(t => t.isImportant && !t.isCompleted).length,
            overdue: tasks.filter(t => t.dueDate && t.dueDate < today && !t.isCompleted).length,
            completedToday: tasks.filter(t => 
                t.completedAt && t.completedAt.split('T')[0] === today
            ).length
        };
    }

    // UI state management methods
    setSearchTerm(term) {
        this.state.searchTerm = term;
        this.cache.set('searchTerm', term);
        this.ui.render();
    }

    setSortBy(sortBy) {
        this.state.sortBy = sortBy;
        this.cache.set('sortBy', sortBy);
        this.ui.render();
    }

    switchList(listId) {
        this.state.currentList = listId;
        this.cache.set('currentList', listId);
        this.ui.render();
    }

    toggleTheme() {
        this.state.theme = this.state.theme === 'light' ? 'dark' : 'light';
        this.cache.set('theme', this.state.theme);
        document.documentElement.setAttribute('data-theme', this.state.theme);
    }

    // Task management methods
    addTask(taskData) {
        return this.createTask(taskData);
    }

    addList(listName) {
        console.log('TodoApp.addList called with:', listName);
        return this.createList({ name: listName });
    }

    // Conflict resolution methods
    applyConflictResolution(conflict, resolution) {
        // Apply the resolution to local state
        const { key, localData, serverData } = conflict;
        
        switch (resolution.strategy) {
            case 'local-wins':
                this.state[key] = localData;
                break;
            case 'server-wins':
                this.state[key] = serverData;
                break;
            case 'merge':
                this.state[key] = resolution.mergedData;
                break;
        }
        
        // Update cache
        this.cache.set(key, this.state[key]);
        
        // Remove conflict
        this.cache.removeConflict(conflict.id);
        
        // Re-render
        this.ui.render();
    }

    setAutoResolveStrategy(key, strategy) {
        this.autoResolveStrategies[key] = strategy;
        this.cache.set('autoResolveStrategies', this.autoResolveStrategies);
    }
}

// Initialize the app when DOM is loaded
let todoApp;
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🔧 DOM Content Loaded - Starting Todo App...');
    try {
        todoApp = new TodoApp();
        window.todoApp = todoApp; // Make it globally accessible for onclick handlers
        await todoApp.init(); // Properly await the initialization
        console.log('🎉 Todo App ready!');
    } catch (error) {
        console.error('💥 Failed to start Todo App:', error);
        // Show user-friendly error message
        document.body.innerHTML = `
            <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                <h2 style="color: #d32f2f;">⚠️ Application Error</h2>
                <p>Failed to load the Todo application.</p>
                <p><strong>Please ensure the API server is running at <code>http://localhost:5000</code></strong></p>
                <p>Error: ${error.message}</p>
                <button onclick="location.reload()" style="padding: 10px 20px; margin: 10px; font-size: 14px;">🔄 Retry</button>
                <br><br>
                <p style="font-size: 12px; color: #666;">
                    To start the API server, run: <code>cd todoapi && dotnet run</code>
                </p>
            </div>
        `;
    }
});
