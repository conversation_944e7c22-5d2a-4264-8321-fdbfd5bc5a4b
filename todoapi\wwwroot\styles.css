:root {
    /* Light theme */
    --primary-color: #0078d4;
    --background-primary: #ffffff;
    --background-secondary: #f3f2f1;
    --text-primary: #323130;
    --text-secondary: #605e5c;
    --surface-border: #edebe9;
    --surface-hover: #f3f2f1;
    --success-color: #107c10;
    --warning-color: #ff8c00;
    --error-color: #d13438;
}

[data-theme="dark"] {
    --primary-color: #0086f0;
    --background-primary: #1e1e1e;
    --background-secondary: #2d2c2c;
    --text-primary: #ffffff;
    --text-secondary: #b3b0ad;
    --surface-border: #3b3a39;
    --surface-hover: #3b3a39;
    --success-color: #54b054;
    --warning-color: #ffaa44;
    --error-color: #f85149;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON>e <PERSON>', <PERSON><PERSON>, sans-serif;
    background-color: var(--background-primary);
    color: var(--text-primary);
    line-height: 1.5;
    transition: background-color 0.3s, color 0.3s;
}

.app-container {
    display: flex;
    height: 100vh;
    max-width: 1400px;
    margin: 0 auto;
}

/* Sidebar */
.sidebar {
    width: 280px;
    background-color: var(--background-secondary);
    border-right: 1px solid var(--surface-border);
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.app-header {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;
    row-gap: 15px;
}

.app-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0;
}

.search-container {
    position: relative;
    margin-bottom: 0;
    flex-basis: 100%;
}

.search-input {
    width: 100%;
    padding: 10px 12px 10px 36px;
    border: 1px solid var(--surface-border);
    border-radius: 6px;
    background-color: var(--background-primary);
    color: var(--text-primary);
    font-size: 14px;
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.list-section {
    margin-bottom: 30px;
}

.section-title {
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    color: var(--text-secondary);
    margin-bottom: 12px;
    letter-spacing: 0.5px;
}

.list-item, .user-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
    margin-bottom: 2px;
    position: relative;
}

.list-item:hover, .user-item:hover {
    background-color: var(--surface-hover);
}

.list-item.active, .user-item.active {
    background-color: var(--primary-color);
    color: white;
}

.list-item .icon, .user-item .icon {
    margin-right: 12px;
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.list-item span:nth-child(2), .user-item span:nth-child(2) {
    flex: 1;
    font-size: 14px;
}

.list-count {
    font-size: 12px;
    background-color: var(--surface-border);
    color: var(--text-secondary);
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 20px;
    text-align: center;
}

.list-item.active .list-count {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.new-list-form {
    margin-top: 8px;
}

.new-list-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid transparent;
    border-radius: 6px;
    background: transparent;
    color: var(--text-secondary);
    font-size: 14px;
    transition: all 0.2s;
}

.new-list-input:hover {
    background-color: var(--surface-hover);
}

.new-list-input:focus {
    outline: none;
    border-color: var(--primary-color);
    background-color: var(--background-primary);
    color: var(--text-primary);
}

.list-delete {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    opacity: 0;
    transition: opacity 0.2s;
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
}

.list-item:hover .list-delete {
    opacity: 1;
}

.list-delete:hover {
    background-color: var(--surface-hover);
    color: #d13438;
}

.user-avatar {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 12px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;
    flex-shrink: 0;
}

/* Main Content */
.content {
    flex: 1;
    padding: 20px 30px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 16px;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.sort-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.sort-label {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

.sort-select {
    padding: 6px 8px;
    border: 1px solid var(--surface-border);
    border-radius: 4px;
    background-color: var(--background-secondary);
    color: var(--text-primary);
    font-size: 12px;
    cursor: pointer;
    min-width: 160px;
}

.sort-select:focus {
    outline: none;
    border-color: var(--primary-color);
}

.sort-select:hover {
    background-color: var(--surface-hover);
}

.toolbar {
    display: flex;
    gap: 8px;
}

.toolbar-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 8px;
    background-color: var(--background-secondary);
    border: 1px solid var(--surface-border);
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s, border-color 0.2s;
    font-family: inherit;
    color: var(--text-secondary);
    min-width: 70px;
    height: 60px;
    text-align: center;
}

.toolbar-btn:hover {
    background-color: var(--surface-hover);
    border-color: var(--primary-color);
}

.toolbar-icon {
    font-size: 20px;
    line-height: 1;
}

.toolbar-label {
    font-size: 12px;
    font-weight: 500;
    line-height: 1.1;
}

.list-title {
    font-size: 28px;
    font-weight: 600;
    color: var(--text-primary);
}

.task-stats {
    color: var(--text-secondary);
    font-size: 14px;
}

.analytics-panel {
    background: var(--background-secondary);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    border: 1px solid var(--surface-border);
}

.analytics-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--text-primary);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 12px;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 20px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: var(--text-secondary);
}

.tasks {
    margin-bottom: 20px;
    flex: 1;
    overflow-y: auto;
}

.task-item {
    display: flex;
    align-items: flex-start;
    padding: 12px 16px;
    background-color: var(--background-primary);
    border: 1px solid var(--surface-border);
    border-radius: 8px;
    margin-bottom: 8px;
    transition: all 0.2s;
    position: relative;
}

.task-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 120, 212, 0.1);
}

.task-item.completed {
    opacity: 0.6;
}

.task-item.completed .task-title {
    text-decoration: line-through;
}

.task-item.important {
    border-left: 4px solid var(--warning-color);
}

.task-item.selected {
    background-color: rgba(0, 120, 212, 0.1);
    border-color: var(--primary-color);
}

.task-checkbox {
    width: 20px;
    height: 20px;
    border: 2px solid var(--surface-border);
    border-radius: 50%;
    background: none;
    cursor: pointer;
    margin-right: 12px;
    margin-top: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    flex-shrink: 0;
}

.task-checkbox:hover {
    border-color: var(--primary-color);
}

.task-item.completed .task-checkbox {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.task-item .task-select {
    position: absolute;
    right: 10px;
    top: 10px;
}

.task-content {
    flex: 1;
    cursor: pointer;
}

.task-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
    line-height: 1.4;
}

.task-notes {
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 8px;
    line-height: 1.3;
}

.task-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: var(--text-secondary);
    flex-wrap: wrap;
}

.task-assignees {
    display: flex;
    gap: 4px;
    margin-top: 8px;
}

.assignee-avatar {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;
}

.task-actions {
    display: flex;
    gap: 4px;
    margin-left: 12px;
    opacity: 0;
    transition: opacity 0.2s;
}

.task-item:hover .task-actions {
    opacity: 1;
}

.task-important, .task-delete, .task-edit, .task-duplicate {
    background: none;
    border: none;
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.2s;
}

.task-important:hover {
    background-color: var(--surface-hover);
    color: var(--warning-color);
}

.task-important.active {
    color: var(--warning-color);
}

.task-delete:hover {
    background-color: var(--surface-hover);
    color: var(--error-color);
}

.task-edit:hover {
    background-color: var(--surface-hover);
    color: var(--primary-color);
}

.task-duplicate:hover {
    background-color: var(--surface-hover);
    color: var(--primary-color);
}

/* Task Action Tooltips */
.task-action-btn {
    position: relative;
}

.task-action-btn::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--text-primary);
    color: var(--background-primary);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s, visibility 0.2s;
    z-index: 1000;
    pointer-events: none;
    margin-bottom: 4px;
}

.task-action-btn:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Arrow for tooltip */
.task-action-btn::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--text-primary);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s, visibility 0.2s;
    z-index: 1000;
    pointer-events: none;
}

.task-action-btn:hover::before {
    opacity: 1;
    visibility: visible;
}

.task-input {
    background-color: var(--background-primary);
    border-top: 1px solid var(--surface-border);
    padding: 16px 0 0;
    margin-top: auto;
}

.task-input-form {
    display: flex;
    gap: 12px;
    align-items: center;
}

.new-task-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid var(--surface-border);
    border-radius: 6px;
    background-color: var(--background-secondary);
    color: var(--text-primary);
    font-size: 14px;
}

.new-task-date {
    padding: 12px 16px;
    border: 1px solid var(--surface-border);
    border-radius: 6px;
    background-color: var(--background-secondary);
    color: var(--text-primary);
    font-size: 14px;
    width: 150px;
}

.new-task-input:focus, .new-task-date:focus {
    outline: none;
    border-color: var(--primary-color);
}

.add-task-button {
    padding: 12px 20px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.add-task-button:hover {
    background-color: #106ebe;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-secondary);
}

.empty-state .icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.empty-state h3 {
    font-size: 18px;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.theme-toggle {
    background-color: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--surface-border);
    border-radius: 6px;
    width: 36px;
    height: 36px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.theme-toggle:hover {
    background-color: var(--surface-hover);
}

.header-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.help-button {
    background-color: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--surface-border);
    border-radius: 6px;
    width: 36px;
    height: 36px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.help-button:hover {
    background-color: var(--surface-hover);
    color: var(--primary-color);
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-overlay.show {
    display: flex;
}

.modal {
    background: var(--background-primary);
    border-radius: 8px;
    padding: 24px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-secondary);
}

.form-group {
    margin-bottom: 16px;
}

.form-label {
    display: block;
    margin-bottom: 6px;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
}

.form-input, .form-textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--surface-border);
    border-radius: 6px;
    background-color: var(--background-secondary);
    color: var(--text-primary);
    font-size: 14px;
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

.modal-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
}

/* Custom Assignee Select in Modal */
.assignee-list {
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid var(--surface-border);
    border-radius: 6px;
    background-color: var(--background-secondary);
}

.assignee-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s;
    user-select: none;
}

.assignee-item:hover {
    background-color: var(--surface-hover);
}

.assignee-item .user-avatar {
    margin-right: 12px;
}

.assignee-item span:not(.checkmark) {
    flex: 1;
}

.assignee-item .checkmark {
    font-size: 16px;
    color: var(--primary-color);
    font-weight: bold;
    width: 20px;
    text-align: center;
}

[data-theme="dark"] .assignee-item.selected {
     background-color: rgba(0, 134, 240, 0.2);
}

[data-theme="light"] .assignee-item.selected {
    background-color: rgba(0, 120, 212, 0.1);
}

.btn {
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #106ebe;
}

.btn-secondary {
    background-color: var(--surface-border);
    color: var(--text-primary);
}

.btn-secondary:hover {
    background-color: var(--surface-hover);
}

/* Bulk Actions */
.bulk-actions {
    display: none;
    background: var(--background-secondary);
    border: 1px solid var(--surface-border);
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 16px;
    align-items: center;
    gap: 12px;
}

.bulk-actions.show {
    display: flex;
}

.bulk-info {
    flex: 1;
    font-size: 14px;
    color: var(--text-primary);
}

.bulk-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.bulk-complete {
    background-color: var(--success-color);
    color: white;
}

.bulk-delete {
    background-color: var(--error-color);
    color: white;
}

.bulk-cancel {
    background-color: var(--surface-border);
    color: var(--text-primary);
}

.overdue {
    color: var(--error-color);
}

/* Drag and Drop Styles */
.task-item[draggable="true"] {
    cursor: grab;
}

.task-item.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
    cursor: grabbing;
    z-index: 1000;
}

.task-item.drag-over {
    border-top: 3px solid var(--primary-color);
    margin-top: 3px;
}

.list-item.drag-over {
    background-color: rgba(0, 120, 212, 0.1);
    border: 2px dashed var(--primary-color);
}

.drop-zone {
    min-height: 40px;
    border: 2px dashed transparent;
    border-radius: 8px;
    margin: 4px 0;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: 14px;
}

.drop-zone.active {
    border-color: var(--primary-color);
    background-color: rgba(0, 120, 212, 0.05);
    color: var(--primary-color);
}

.drag-handle {
    cursor: grab;
    color: var(--text-secondary);
    padding: 4px;
    opacity: 0;
    transition: opacity 0.2s;
}

.task-item:hover .drag-handle {
    opacity: 1;
}

.drag-handle:active {
    cursor: grabbing;
}

/* Keyboard Shortcuts Modal Styles */
.shortcuts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.shortcut-section h4 {
    color: var(--primary-color);
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: 600;
}

.shortcut-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid var(--surface-border);
}

.shortcut-item:last-child {
    border-bottom: none;
}

.shortcut-item kbd {
    background-color: var(--background-secondary);
    border: 1px solid var(--surface-border);
    border-radius: 4px;
    padding: 2px 6px;
    font-size: 11px;
    font-family: monospace;
    color: var(--text-primary);
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.shortcut-item span {
    font-size: 13px;
    color: var(--text-secondary);
}

.shortcuts-note {
    background-color: var(--background-secondary);
    border-radius: 6px;
    padding: 12px;
    margin-top: 16px;
}

.shortcuts-note p {
    margin: 0;
    font-size: 13px;
    color: var(--text-secondary);
}

/* Conflict Resolution Modal Styles */
.conflict-modal {
    max-width: 800px;
    width: 90vw;
}

.conflict-description {
    background-color: var(--background-secondary);
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 20px;
    border-left: 4px solid var(--warning-color);
}

.conflict-comparison {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 20px;
    margin-bottom: 24px;
    align-items: start;
}

.conflict-side h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.conflict-data {
    background-color: var(--background-secondary);
    border-radius: 6px;
    padding: 16px;
    border: 1px solid var(--surface-border);
}

.data-preview {
    font-family: monospace;
    font-size: 12px;
    color: var(--text-secondary);
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-word;
}

.data-timestamp {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid var(--surface-border);
    font-size: 11px;
    color: var(--text-tertiary);
}

.conflict-divider {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0;
}

.vs-indicator {
    background-color: var(--primary-color);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 12px;
}

.resolution-options h4 {
    margin: 0 0 16px 0;
    font-size: 14px;
    color: var(--text-primary);
}

.resolution-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.resolution-buttons .btn {
    flex: 1;
    min-width: 160px;
    padding: 12px 16px;
    font-size: 13px;
    transition: all 0.2s;
}

.resolution-buttons .btn.selected {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.auto-resolution {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--surface-border);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 13px;
    color: var(--text-secondary);
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

/* Sync Status Indicator */
.sync-status {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: var(--background-primary);
    border: 1px solid var(--surface-border);
    border-radius: 8px;
    padding: 8px 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 12px;
    z-index: 1000;
}

.sync-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
}

.sync-icon {
    font-size: 14px;
}

.sync-text {
    color: var(--text-secondary);
}

.conflict-badge {
    background-color: var(--error-color);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
}

.conflict-badge:hover {
    background-color: var(--error-hover);
}

.sync-status.syncing .sync-icon {
    animation: spin 1s linear infinite;
}

.sync-status.error .sync-icon {
    color: var(--error-color);
}

.sync-status.success .sync-icon {
    color: var(--success-color);
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Mobile responsiveness for conflict modal */
@media (max-width: 768px) {
    .conflict-modal {
        width: 95vw;
        max-width: none;
    }

    .conflict-comparison {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .conflict-divider {
        padding: 10px 0;
    }

    .vs-indicator {
        transform: rotate(90deg);
    }

    .resolution-buttons {
        flex-direction: column;
    }

    .resolution-buttons .btn {
        min-width: auto;
    }
}

@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
        height: auto;
        min-height: 100vh;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
        max-height: 40vh;
        border-right: none;
        border-bottom: 1px solid var(--surface-border);
    }
    
    .content {
        padding: 15px;
    }

    .new-task-date {
        width: auto;
    }

    .task-input-form {
        flex-wrap: wrap;
    }
    
    .new-task-input {
        flex-basis: 100%;
    }
}

/* Print Styles */
@media print {
    body, html {
        background: #fff !important;
        color: #000 !important;
    }
    .sidebar, .task-input, .list-header, .analytics-panel, .bulk-actions, .task-actions, .task-checkbox, .task-select, .theme-toggle, .search-container, .list-section {
        display: none !important;
    }
    .app-container, .content, body {
        display: block !important;
        padding: 0 !important;
        margin: 0 !important;
        border: none !important;
        height: auto !important;
        overflow: visible !important;
        box-shadow: none !important;
    }
    .tasks {
        margin: 0;
        padding: 0;
        overflow: visible !important;
    }
    .task-item {
        border: 1px solid #ccc !important;
        box-shadow: none !important;
        page-break-inside: avoid;
    }
    .task-item.completed .task-title {
        color: #888;
    }
    body::before {
        content: 'Todo List: ' attr(data-list-title);
        display: block;
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
        text-align: center;
    }
}

