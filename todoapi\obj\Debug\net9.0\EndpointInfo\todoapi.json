{"openapi": "3.0.4", "info": {"title": "Todo API", "description": "A comprehensive Todo API with SQLite database", "version": "v1"}, "paths": {"/api/Lists": {"get": {"tags": ["Lists"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TodoListDtoIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TodoListDtoIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TodoListDtoIEnumerableApiResponse"}}}}}}, "post": {"tags": ["Lists"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTodoListDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateTodoListDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateTodoListDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TodoListDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TodoListDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TodoListDtoApiResponse"}}}}}}}, "/api/Lists/{id}": {"get": {"tags": ["Lists"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TodoListDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TodoListDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TodoListDtoApiResponse"}}}}}}, "put": {"tags": ["Lists"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTodoListDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateTodoListDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateTodoListDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TodoListDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TodoListDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TodoListDtoApiResponse"}}}}}}, "delete": {"tags": ["Lists"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}}}}}}, "/api/Stats": {"get": {"tags": ["Stats"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}}}}}}, "/api/Tasks": {"get": {"tags": ["Tasks"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoIEnumerableApiResponse"}}}}}}, "post": {"tags": ["Tasks"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTodoTaskDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateTodoTaskDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateTodoTaskDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoApiResponse"}}}}}}}, "/api/Tasks/{id}": {"get": {"tags": ["Tasks"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoApiResponse"}}}}}}, "put": {"tags": ["Tasks"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTodoTaskDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateTodoTaskDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateTodoTaskDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoApiResponse"}}}}}}, "delete": {"tags": ["Tasks"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}}}}}}, "/api/Tasks/bulk": {"post": {"tags": ["Tasks"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkOperationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkOperationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkOperationDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}}}}}}, "/api/Tasks/by-list/{listId}": {"get": {"tags": ["Tasks"], "parameters": [{"name": "listId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoIEnumerableApiResponse"}}}}}}}, "/api/Tasks/by-user/{userId}": {"get": {"tags": ["Tasks"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoIEnumerableApiResponse"}}}}}}}, "/api/Tasks/important": {"get": {"tags": ["Tasks"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoIEnumerableApiResponse"}}}}}}}, "/api/Tasks/my-day/{date}": {"get": {"tags": ["Tasks"], "parameters": [{"name": "date", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoIEnumerableApiResponse"}}}}}}}, "/api/Tasks/planned": {"get": {"tags": ["Tasks"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TodoTaskDtoIEnumerableApiResponse"}}}}}}}, "/api/Users": {"get": {"tags": ["Users"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDtoIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDtoIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDtoIEnumerableApiResponse"}}}}}}, "post": {"tags": ["Users"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}}}}}}, "/api/Users/<USER>": {"get": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}}}}}, "put": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}}}}}, "delete": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}}}}}}, "/WeatherForecast": {"get": {"tags": ["WeatherForecast"], "operationId": "GetWeatherForecast", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}}, "components": {"schemas": {"AssigneeDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "avatar": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BooleanApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "BulkOperationDto": {"type": "object", "properties": {"taskIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "operation": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateTodoListDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateTodoTaskDto": {"type": "object", "properties": {"title": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "dueDate": {"type": "string", "format": "date-time", "nullable": true}, "recurrence": {"type": "string", "nullable": true}, "isImportant": {"type": "boolean"}, "myDayDate": {"type": "string", "nullable": true}, "listId": {"type": "string", "nullable": true}, "assigneeIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "CreateUserDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "avatar": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ObjectApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "TodoListDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "smart": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "taskCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "TodoListDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/TodoListDto"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "TodoListDtoIEnumerableApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TodoListDto"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "TodoTaskDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "isCompleted": {"type": "boolean"}, "isImportant": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "completedAt": {"type": "string", "format": "date-time", "nullable": true}, "dueDate": {"type": "string", "format": "date-time", "nullable": true}, "recurrence": {"type": "string", "nullable": true}, "myDayDate": {"type": "string", "nullable": true}, "listId": {"type": "string", "nullable": true}, "assignees": {"type": "array", "items": {"$ref": "#/components/schemas/AssigneeDto"}, "nullable": true}}, "additionalProperties": false}, "TodoTaskDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/TodoTaskDto"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "TodoTaskDtoIEnumerableApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TodoTaskDto"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UpdateTodoTaskDto": {"type": "object", "properties": {"title": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "isCompleted": {"type": "boolean", "nullable": true}, "isImportant": {"type": "boolean", "nullable": true}, "dueDate": {"type": "string", "format": "date-time", "nullable": true}, "recurrence": {"type": "string", "nullable": true}, "myDayDate": {"type": "string", "nullable": true}, "listId": {"type": "string", "nullable": true}, "assigneeIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "avatar": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "taskCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UserDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/UserDto"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UserDtoIEnumerableApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "WeatherForecast": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}