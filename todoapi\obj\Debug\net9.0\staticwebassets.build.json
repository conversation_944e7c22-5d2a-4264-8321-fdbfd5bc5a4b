{"Version": 1, "Hash": "Iu5okVVNnFqm3be9nPoVrgXWykaCvVd5npJUxVhcQg0=", "Source": "to<PERSON>ap<PERSON>", "BasePath": "_content/todoapi", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "todoapi\\wwwroot", "Source": "to<PERSON>ap<PERSON>", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "Pattern": "**"}], "Assets": [{"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\2939h73z6q-q7oz31h1by.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "index9-clean#[.{fingerprint=q7oz31h1by}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9-clean.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e80im4fdr1", "Integrity": "rmcl4NLapeBROJihhf6i27bvTpoeV8nl1TsyesALXv8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9-clean.html", "FileLength": 3313, "LastWriteTime": "2025-09-28T13:44:20+00:00"}, {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\9vecr4ppa8-olqgrn1tj4.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "index9#[.{fingerprint=olqgrn1tj4}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ckmgm6fb0o", "Integrity": "xMshiibop0Kb+GD1LFu6Hjo84UoCW2+DhOO1HDIAmug=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9.html", "FileLength": 3314, "LastWriteTime": "2025-09-28T13:47:44+00:00"}, {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\a9fs2lewzf-5lbvunm6pn.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "backup/index#[.{fingerprint=5lbvunm6pn}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sfkj51iytr", "Integrity": "OFeln9w3U2lkCl5GtvBgJtU2fh1GaGB0HrJkH+f0wrs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index.html", "FileLength": 760, "LastWriteTime": "2025-09-28T13:44:20+00:00"}, {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\ae8ekgrttx-mb95wunvkt.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "styles#[.{fingerprint=mb95wunvkt}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5k9yzxuzo1", "Integrity": "5SsbBNT/tJF3BhUIoOeC6Hpd9FQozc56iUv2Qap7q9I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\styles.css", "FileLength": 4360, "LastWriteTime": "2025-09-28T13:44:20+00:00"}, {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\hp8lip8dl5-lntny8cd3c.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "app#[.{fingerprint=lntny8cd3c}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ribl8xwmof", "Integrity": "k9kaQHrddU4x76jwtJ+EzHZ9U8cPYjGWB52wnBq4XRA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\app.js", "FileLength": 3581, "LastWriteTime": "2025-09-28T13:47:44+00:00"}, {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\me9ah3j1xc-yirdgu4cai.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "todo-app#[.{fingerprint=yirdgu4cai}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\todo-app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d1ivtdx6iu", "Integrity": "73F3jtJKw8iLgSygXmbs0RMmml4rdmMezHI3HpY7jiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\todo-app.js", "FileLength": 4659, "LastWriteTime": "2025-09-28T13:47:44+00:00"}, {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\qje2162odc-uk5yjv168n.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "backup/index9 - Copy#[.{fingerprint=uk5yjv168n}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index9 - Copy.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j34but2afx", "Integrity": "gQTTL93AbFv+lBWIRxh5OVS81Yz4rVAtF0z9xUM/SMQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index9 - Copy.html", "FileLength": 21251, "LastWriteTime": "2025-09-28T13:44:20+00:00"}, {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\qkbdjfiydr-595uqhdyu2.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "index9-backup#[.{fingerprint=595uqhdyu2}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9-backup.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f2ppdhzrod", "Integrity": "J0wQBfJ/jM46tUFbkcLrW26DGvVvT9zjpd6hWDQulag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9-backup.html", "FileLength": 16997, "LastWriteTime": "2025-09-28T13:44:20+00:00"}, {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\uvhoxifli3-zhln9tevi0.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "ui-manager#[.{fingerprint=zhln9tevi0}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\ui-manager.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j2j7ym9x2g", "Integrity": "S6Y2dIN39rkBvg2Af8QgOB9BTy/W1v1ebCYkn5vqejM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\ui-manager.js", "FileLength": 7057, "LastWriteTime": "2025-09-28T13:47:44+00:00"}, {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\app.js", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lntny8cd3c", "Integrity": "H4gxE1xMACmg7wuf/flp2mMTGKwfv5mwKRRaTBoCjRY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.js", "FileLength": 16435, "LastWriteTime": "2025-09-28T13:46:38+00:00"}, {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index.html", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "backup/index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5lbvunm6pn", "Integrity": "lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backup\\index.html", "FileLength": 1941, "LastWriteTime": "2025-09-28T03:04:20+00:00"}, {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index9 - Copy.html", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "backup/index9 - Copy#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "uk5yjv168n", "Integrity": "vq9BTSuUyPJUcsH6XlQUs9F9kV3CaujKXxugiI9tTho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backup\\index9 - Copy.html", "FileLength": 133787, "LastWriteTime": "2025-09-28T13:11:54+00:00"}, {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9.html", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "index9#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "olqgrn1tj4", "Integrity": "VA5sqO/RUbif/XskndwzuQNSH2ux7gWVWisbWAmTSjU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index9.html", "FileLength": 19419, "LastWriteTime": "2025-09-28T13:47:18+00:00"}, {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9-backup.html", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "index9-backup#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "595uqhdyu2", "Integrity": "GQRjVfF7pbEugsQ7SDV9U/FuUPuY5ZOD9tVJiNerGB8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index9-backup.html", "FileLength": 100054, "LastWriteTime": "2025-09-28T13:24:04+00:00"}, {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9-clean.html", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "index9-clean#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "q7oz31h1by", "Integrity": "qsNdnqEp0inrPmO8N/ZnGqNgSDA61ZyrmjWpC/at1QU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index9-clean.html", "FileLength": 19835, "LastWriteTime": "2025-09-28T13:43:06+00:00"}, {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\styles.css", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "styles#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mb95wunvkt", "Integrity": "jrlspTk8eSbhsrt52DJGB/Nz9GniPonpBzfSfcVbagM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\styles.css", "FileLength": 26007, "LastWriteTime": "2025-09-28T13:43:06+00:00"}, {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\todo-app.js", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "todo-app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "yirdgu4cai", "Integrity": "mlSsPrANl7IrzlrokoEDKJSmbLi4QBbcYdmBQzgO/OE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\todo-app.js", "FileLength": 20502, "LastWriteTime": "2025-09-28T13:46:38+00:00"}, {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\ui-manager.js", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "ui-manager#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zhln9tevi0", "Integrity": "aVY1Nq6bMgvQ71moBOwjdYBcuWJcjIAAt1g4+XIsTFE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\ui-manager.js", "FileLength": 35289, "LastWriteTime": "2025-09-28T13:46:38+00:00"}], "Endpoints": [{"Route": "app.js", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\hp8lip8dl5-lntny8cd3c.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000279173646"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3581"}, {"Name": "ETag", "Value": "\"k9kaQHrddU4x76jwtJ+EzHZ9U8cPYjGWB52wnBq4XRA=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"H4gxE1xMACmg7wuf/flp2mMTGKwfv5mwKRRaTBoCjRY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H4gxE1xMACmg7wuf/flp2mMTGKwfv5mwKRRaTBoCjRY="}]}, {"Route": "app.js", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16435"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"H4gxE1xMACmg7wuf/flp2mMTGKwfv5mwKRRaTBoCjRY=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:46:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H4gxE1xMACmg7wuf/flp2mMTGKwfv5mwKRRaTBoCjRY="}]}, {"Route": "app.js.gz", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\hp8lip8dl5-lntny8cd3c.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3581"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"k9kaQHrddU4x76jwtJ+EzHZ9U8cPYjGWB52wnBq4XRA=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k9kaQHrddU4x76jwtJ+EzHZ9U8cPYjGWB52wnBq4XRA="}]}, {"Route": "app.lntny8cd3c.js", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\hp8lip8dl5-lntny8cd3c.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000279173646"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3581"}, {"Name": "ETag", "Value": "\"k9kaQHrddU4x76jwtJ+EzHZ9U8cPYjGWB52wnBq4XRA=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"H4gxE1xMACmg7wuf/flp2mMTGKwfv5mwKRRaTBoCjRY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lntny8cd3c"}, {"Name": "label", "Value": "app.js"}, {"Name": "integrity", "Value": "sha256-H4gxE1xMACmg7wuf/flp2mMTGKwfv5mwKRRaTBoCjRY="}]}, {"Route": "app.lntny8cd3c.js", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16435"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"H4gxE1xMACmg7wuf/flp2mMTGKwfv5mwKRRaTBoCjRY=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:46:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lntny8cd3c"}, {"Name": "label", "Value": "app.js"}, {"Name": "integrity", "Value": "sha256-H4gxE1xMACmg7wuf/flp2mMTGKwfv5mwKRRaTBoCjRY="}]}, {"Route": "app.lntny8cd3c.js.gz", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\hp8lip8dl5-lntny8cd3c.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3581"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"k9kaQHrddU4x76jwtJ+EzHZ9U8cPYjGWB52wnBq4XRA=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lntny8cd3c"}, {"Name": "label", "Value": "app.js.gz"}, {"Name": "integrity", "Value": "sha256-k9kaQHrddU4x76jwtJ+EzHZ9U8cPYjGWB52wnBq4XRA="}]}, {"Route": "backup/index.5lbvunm6pn.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\a9fs2lewzf-5lbvunm6pn.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001314060447"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "760"}, {"Name": "ETag", "Value": "\"OFeln9w3U2lkCl5GtvBgJtU2fh1GaGB0HrJkH+f0wrs=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5lbvunm6pn"}, {"Name": "label", "Value": "backup/index.html"}, {"Name": "integrity", "Value": "sha256-lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ="}]}, {"Route": "backup/index.5lbvunm6pn.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1941"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 03:04:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5lbvunm6pn"}, {"Name": "label", "Value": "backup/index.html"}, {"Name": "integrity", "Value": "sha256-lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ="}]}, {"Route": "backup/index.5lbvunm6pn.html.gz", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\a9fs2lewzf-5lbvunm6pn.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "760"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"OFeln9w3U2lkCl5GtvBgJtU2fh1GaGB0HrJkH+f0wrs=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5lbvunm6pn"}, {"Name": "label", "Value": "backup/index.html.gz"}, {"Name": "integrity", "Value": "sha256-OFeln9w3U2lkCl5GtvBgJtU2fh1GaGB0HrJkH+f0wrs="}]}, {"Route": "backup/index.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\a9fs2lewzf-5lbvunm6pn.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001314060447"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "760"}, {"Name": "ETag", "Value": "\"OFeln9w3U2lkCl5GtvBgJtU2fh1GaGB0HrJkH+f0wrs=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ="}]}, {"Route": "backup/index.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1941"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 03:04:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ="}]}, {"Route": "backup/index.html.gz", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\a9fs2lewzf-5lbvunm6pn.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "760"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"OFeln9w3U2lkCl5GtvBgJtU2fh1GaGB0HrJkH+f0wrs=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OFeln9w3U2lkCl5GtvBgJtU2fh1GaGB0HrJkH+f0wrs="}]}, {"Route": "backup/index9 - Copy.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\qje2162odc-uk5yjv168n.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000047054395"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21251"}, {"Name": "ETag", "Value": "\"gQTTL93AbFv+lBWIRxh5OVS81Yz4rVAtF0z9xUM/SMQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"vq9BTSuUyPJUcsH6XlQUs9F9kV3CaujKXxugiI9tTho=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vq9BTSuUyPJUcsH6XlQUs9F9kV3CaujKXxugiI9tTho="}]}, {"Route": "backup/index9 - Copy.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index9 - Copy.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "133787"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"vq9BTSuUyPJUcsH6XlQUs9F9kV3CaujKXxugiI9tTho=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vq9BTSuUyPJUcsH6XlQUs9F9kV3CaujKXxugiI9tTho="}]}, {"Route": "backup/index9 - Copy.html.gz", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\qje2162odc-uk5yjv168n.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21251"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"gQTTL93AbFv+lBWIRxh5OVS81Yz4rVAtF0z9xUM/SMQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gQTTL93AbFv+lBWIRxh5OVS81Yz4rVAtF0z9xUM/SMQ="}]}, {"Route": "backup/index9 - Copy.uk5yjv168n.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\qje2162odc-uk5yjv168n.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000047054395"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21251"}, {"Name": "ETag", "Value": "\"gQTTL93AbFv+lBWIRxh5OVS81Yz4rVAtF0z9xUM/SMQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"vq9BTSuUyPJUcsH6XlQUs9F9kV3CaujKXxugiI9tTho=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uk5yjv168n"}, {"Name": "label", "Value": "backup/index9 - Copy.html"}, {"Name": "integrity", "Value": "sha256-vq9BTSuUyPJUcsH6XlQUs9F9kV3CaujKXxugiI9tTho="}]}, {"Route": "backup/index9 - Copy.uk5yjv168n.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\backup\\index9 - Copy.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "133787"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"vq9BTSuUyPJUcsH6XlQUs9F9kV3CaujKXxugiI9tTho=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uk5yjv168n"}, {"Name": "label", "Value": "backup/index9 - Copy.html"}, {"Name": "integrity", "Value": "sha256-vq9BTSuUyPJUcsH6XlQUs9F9kV3CaujKXxugiI9tTho="}]}, {"Route": "backup/index9 - Copy.uk5yjv168n.html.gz", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\qje2162odc-uk5yjv168n.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21251"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"gQTTL93AbFv+lBWIRxh5OVS81Yz4rVAtF0z9xUM/SMQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uk5yjv168n"}, {"Name": "label", "Value": "backup/index9 - Copy.html.gz"}, {"Name": "integrity", "Value": "sha256-gQTTL93AbFv+lBWIRxh5OVS81Yz4rVAtF0z9xUM/SMQ="}]}, {"Route": "index9.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\9vecr4ppa8-olqgrn1tj4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000301659125"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3314"}, {"Name": "ETag", "Value": "\"xMshiibop0Kb+GD1LFu6Hjo84UoCW2+DhOO1HDIAmug=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"VA5sqO/RUbif/XskndwzuQNSH2ux7gWVWisbWAmTSjU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VA5sqO/RUbif/XskndwzuQNSH2ux7gWVWisbWAmTSjU="}]}, {"Route": "index9.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19419"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"VA5sqO/RUbif/XskndwzuQNSH2ux7gWVWisbWAmTSjU=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VA5sqO/RUbif/XskndwzuQNSH2ux7gWVWisbWAmTSjU="}]}, {"Route": "index9.html.gz", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\9vecr4ppa8-olqgrn1tj4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3314"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"xMshiibop0Kb+GD1LFu6Hjo84UoCW2+DhOO1HDIAmug=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xMshiibop0Kb+GD1LFu6Hjo84UoCW2+DhOO1HDIAmug="}]}, {"Route": "index9.olqgrn1tj4.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\9vecr4ppa8-olqgrn1tj4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000301659125"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3314"}, {"Name": "ETag", "Value": "\"xMshiibop0Kb+GD1LFu6Hjo84UoCW2+DhOO1HDIAmug=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"VA5sqO/RUbif/XskndwzuQNSH2ux7gWVWisbWAmTSjU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "olqgrn1tj4"}, {"Name": "label", "Value": "index9.html"}, {"Name": "integrity", "Value": "sha256-VA5sqO/RUbif/XskndwzuQNSH2ux7gWVWisbWAmTSjU="}]}, {"Route": "index9.olqgrn1tj4.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19419"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"VA5sqO/RUbif/XskndwzuQNSH2ux7gWVWisbWAmTSjU=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "olqgrn1tj4"}, {"Name": "label", "Value": "index9.html"}, {"Name": "integrity", "Value": "sha256-VA5sqO/RUbif/XskndwzuQNSH2ux7gWVWisbWAmTSjU="}]}, {"Route": "index9.olqgrn1tj4.html.gz", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\9vecr4ppa8-olqgrn1tj4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3314"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"xMshiibop0Kb+GD1LFu6Hjo84UoCW2+DhOO1HDIAmug=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "olqgrn1tj4"}, {"Name": "label", "Value": "index9.html.gz"}, {"Name": "integrity", "Value": "sha256-xMshiibop0Kb+GD1LFu6Hjo84UoCW2+DhOO1HDIAmug="}]}, {"Route": "index9-backup.595uqhdyu2.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\qkbdjfiydr-595uqhdyu2.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000058830451"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16997"}, {"Name": "ETag", "Value": "\"J0wQBfJ/jM46tUFbkcLrW26DGvVvT9zjpd6hWDQulag=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"GQRjVfF7pbEugsQ7SDV9U/FuUPuY5ZOD9tVJiNerGB8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "595uqhdyu2"}, {"Name": "label", "Value": "index9-backup.html"}, {"Name": "integrity", "Value": "sha256-GQRjVfF7pbEugsQ7SDV9U/FuUPuY5ZOD9tVJiNerGB8="}]}, {"Route": "index9-backup.595uqhdyu2.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9-backup.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "100054"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"GQRjVfF7pbEugsQ7SDV9U/FuUPuY5ZOD9tVJiNerGB8=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:24:04 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "595uqhdyu2"}, {"Name": "label", "Value": "index9-backup.html"}, {"Name": "integrity", "Value": "sha256-GQRjVfF7pbEugsQ7SDV9U/FuUPuY5ZOD9tVJiNerGB8="}]}, {"Route": "index9-backup.595uqhdyu2.html.gz", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\qkbdjfiydr-595uqhdyu2.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16997"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"J0wQBfJ/jM46tUFbkcLrW26DGvVvT9zjpd6hWDQulag=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "595uqhdyu2"}, {"Name": "label", "Value": "index9-backup.html.gz"}, {"Name": "integrity", "Value": "sha256-J0wQBfJ/jM46tUFbkcLrW26DGvVvT9zjpd6hWDQulag="}]}, {"Route": "index9-backup.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\qkbdjfiydr-595uqhdyu2.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000058830451"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16997"}, {"Name": "ETag", "Value": "\"J0wQBfJ/jM46tUFbkcLrW26DGvVvT9zjpd6hWDQulag=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"GQRjVfF7pbEugsQ7SDV9U/FuUPuY5ZOD9tVJiNerGB8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GQRjVfF7pbEugsQ7SDV9U/FuUPuY5ZOD9tVJiNerGB8="}]}, {"Route": "index9-backup.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9-backup.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "100054"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"GQRjVfF7pbEugsQ7SDV9U/FuUPuY5ZOD9tVJiNerGB8=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:24:04 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GQRjVfF7pbEugsQ7SDV9U/FuUPuY5ZOD9tVJiNerGB8="}]}, {"Route": "index9-backup.html.gz", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\qkbdjfiydr-595uqhdyu2.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16997"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"J0wQBfJ/jM46tUFbkcLrW26DGvVvT9zjpd6hWDQulag=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J0wQBfJ/jM46tUFbkcLrW26DGvVvT9zjpd6hWDQulag="}]}, {"Route": "index9-clean.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\2939h73z6q-q7oz31h1by.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000301750151"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3313"}, {"Name": "ETag", "Value": "\"rmcl4NLapeBROJihhf6i27bvTpoeV8nl1TsyesALXv8=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"qsNdnqEp0inrPmO8N/ZnGqNgSDA61ZyrmjWpC/at1QU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qsNdnqEp0inrPmO8N/ZnGqNgSDA61ZyrmjWpC/at1QU="}]}, {"Route": "index9-clean.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9-clean.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19835"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"qsNdnqEp0inrPmO8N/ZnGqNgSDA61ZyrmjWpC/at1QU=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:43:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qsNdnqEp0inrPmO8N/ZnGqNgSDA61ZyrmjWpC/at1QU="}]}, {"Route": "index9-clean.html.gz", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\2939h73z6q-q7oz31h1by.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3313"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"rmcl4NLapeBROJihhf6i27bvTpoeV8nl1TsyesALXv8=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rmcl4NLapeBROJihhf6i27bvTpoeV8nl1TsyesALXv8="}]}, {"Route": "index9-clean.q7oz31h1by.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\2939h73z6q-q7oz31h1by.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000301750151"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3313"}, {"Name": "ETag", "Value": "\"rmcl4NLapeBROJihhf6i27bvTpoeV8nl1TsyesALXv8=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"qsNdnqEp0inrPmO8N/ZnGqNgSDA61ZyrmjWpC/at1QU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q7oz31h1by"}, {"Name": "label", "Value": "index9-clean.html"}, {"Name": "integrity", "Value": "sha256-qsNdnqEp0inrPmO8N/ZnGqNgSDA61ZyrmjWpC/at1QU="}]}, {"Route": "index9-clean.q7oz31h1by.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9-clean.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19835"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"qsNdnqEp0inrPmO8N/ZnGqNgSDA61ZyrmjWpC/at1QU=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:43:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q7oz31h1by"}, {"Name": "label", "Value": "index9-clean.html"}, {"Name": "integrity", "Value": "sha256-qsNdnqEp0inrPmO8N/ZnGqNgSDA61ZyrmjWpC/at1QU="}]}, {"Route": "index9-clean.q7oz31h1by.html.gz", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\2939h73z6q-q7oz31h1by.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3313"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"rmcl4NLapeBROJihhf6i27bvTpoeV8nl1TsyesALXv8=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q7oz31h1by"}, {"Name": "label", "Value": "index9-clean.html.gz"}, {"Name": "integrity", "Value": "sha256-rmcl4NLapeBROJihhf6i27bvTpoeV8nl1TsyesALXv8="}]}, {"Route": "styles.css", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\ae8ekgrttx-mb95wunvkt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000229305205"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4360"}, {"Name": "ETag", "Value": "\"5SsbBNT/tJF3BhUIoOeC6Hpd9FQozc56iUv2Qap7q9I=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"jrlspTk8eSbhsrt52DJGB/Nz9GniPonpBzfSfcVbagM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jrlspTk8eSbhsrt52DJGB/Nz9GniPonpBzfSfcVbagM="}]}, {"Route": "styles.css", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26007"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"jrlspTk8eSbhsrt52DJGB/Nz9GniPonpBzfSfcVbagM=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:43:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jrlspTk8eSbhsrt52DJGB/Nz9GniPonpBzfSfcVbagM="}]}, {"Route": "styles.css.gz", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\ae8ekgrttx-mb95wunvkt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4360"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5SsbBNT/tJF3BhUIoOeC6Hpd9FQozc56iUv2Qap7q9I=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5SsbBNT/tJF3BhUIoOeC6Hpd9FQozc56iUv2Qap7q9I="}]}, {"Route": "styles.mb95wunvkt.css", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\ae8ekgrttx-mb95wunvkt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000229305205"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4360"}, {"Name": "ETag", "Value": "\"5SsbBNT/tJF3BhUIoOeC6Hpd9FQozc56iUv2Qap7q9I=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"jrlspTk8eSbhsrt52DJGB/Nz9GniPonpBzfSfcVbagM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mb95wunvkt"}, {"Name": "label", "Value": "styles.css"}, {"Name": "integrity", "Value": "sha256-jrlspTk8eSbhsrt52DJGB/Nz9GniPonpBzfSfcVbagM="}]}, {"Route": "styles.mb95wunvkt.css", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26007"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"jrlspTk8eSbhsrt52DJGB/Nz9GniPonpBzfSfcVbagM=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:43:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mb95wunvkt"}, {"Name": "label", "Value": "styles.css"}, {"Name": "integrity", "Value": "sha256-jrlspTk8eSbhsrt52DJGB/Nz9GniPonpBzfSfcVbagM="}]}, {"Route": "styles.mb95wunvkt.css.gz", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\ae8ekgrttx-mb95wunvkt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4360"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5SsbBNT/tJF3BhUIoOeC6Hpd9FQozc56iUv2Qap7q9I=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:44:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mb95wunvkt"}, {"Name": "label", "Value": "styles.css.gz"}, {"Name": "integrity", "Value": "sha256-5SsbBNT/tJF3BhUIoOeC6Hpd9FQozc56iUv2Qap7q9I="}]}, {"Route": "todo-app.js", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\me9ah3j1xc-yirdgu4cai.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000214592275"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4659"}, {"Name": "ETag", "Value": "\"73F3jtJKw8iLgSygXmbs0RMmml4rdmMezHI3HpY7jiM=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"mlSsPrANl7IrzlrokoEDKJSmbLi4QBbcYdmBQzgO/OE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mlSsPrANl7IrzlrokoEDKJSmbLi4QBbcYdmBQzgO/OE="}]}, {"Route": "todo-app.js", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\todo-app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20502"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mlSsPrANl7IrzlrokoEDKJSmbLi4QBbcYdmBQzgO/OE=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:46:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mlSsPrANl7IrzlrokoEDKJSmbLi4QBbcYdmBQzgO/OE="}]}, {"Route": "todo-app.js.gz", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\me9ah3j1xc-yirdgu4cai.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4659"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"73F3jtJKw8iLgSygXmbs0RMmml4rdmMezHI3HpY7jiM=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-73F3jtJKw8iLgSygXmbs0RMmml4rdmMezHI3HpY7jiM="}]}, {"Route": "todo-app.yirdgu4cai.js", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\me9ah3j1xc-yirdgu4cai.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000214592275"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4659"}, {"Name": "ETag", "Value": "\"73F3jtJKw8iLgSygXmbs0RMmml4rdmMezHI3HpY7jiM=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"mlSsPrANl7IrzlrokoEDKJSmbLi4QBbcYdmBQzgO/OE=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yirdgu4cai"}, {"Name": "label", "Value": "todo-app.js"}, {"Name": "integrity", "Value": "sha256-mlSsPrANl7IrzlrokoEDKJSmbLi4QBbcYdmBQzgO/OE="}]}, {"Route": "todo-app.yirdgu4cai.js", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\todo-app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20502"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mlSsPrANl7IrzlrokoEDKJSmbLi4QBbcYdmBQzgO/OE=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:46:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yirdgu4cai"}, {"Name": "label", "Value": "todo-app.js"}, {"Name": "integrity", "Value": "sha256-mlSsPrANl7IrzlrokoEDKJSmbLi4QBbcYdmBQzgO/OE="}]}, {"Route": "todo-app.yirdgu4cai.js.gz", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\me9ah3j1xc-yirdgu4cai.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4659"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"73F3jtJKw8iLgSygXmbs0RMmml4rdmMezHI3HpY7jiM=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yirdgu4cai"}, {"Name": "label", "Value": "todo-app.js.gz"}, {"Name": "integrity", "Value": "sha256-73F3jtJKw8iLgSygXmbs0RMmml4rdmMezHI3HpY7jiM="}]}, {"Route": "ui-manager.js", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\uvhoxifli3-zhln9tevi0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000141683196"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7057"}, {"Name": "ETag", "Value": "\"S6Y2dIN39rkBvg2Af8QgOB9BTy/W1v1ebCYkn5vqejM=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"aVY1Nq6bMgvQ71moBOwjdYBcuWJcjIAAt1g4+XIsTFE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aVY1Nq6bMgvQ71moBOwjdYBcuWJcjIAAt1g4+XIsTFE="}]}, {"Route": "ui-manager.js", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\ui-manager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "35289"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"aVY1Nq6bMgvQ71moBOwjdYBcuWJcjIAAt1g4+XIsTFE=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:46:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aVY1Nq6bMgvQ71moBOwjdYBcuWJcjIAAt1g4+XIsTFE="}]}, {"Route": "ui-manager.js.gz", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\uvhoxifli3-zhln9tevi0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7057"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"S6Y2dIN39rkBvg2Af8QgOB9BTy/W1v1ebCYkn5vqejM=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S6Y2dIN39rkBvg2Af8QgOB9BTy/W1v1ebCYkn5vqejM="}]}, {"Route": "ui-manager.zhln9tevi0.js", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\uvhoxifli3-zhln9tevi0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000141683196"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7057"}, {"Name": "ETag", "Value": "\"S6Y2dIN39rkBvg2Af8QgOB9BTy/W1v1ebCYkn5vqejM=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"aVY1Nq6bMgvQ71moBOwjdYBcuWJcjIAAt1g4+XIsTFE=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zhln9tevi0"}, {"Name": "label", "Value": "ui-manager.js"}, {"Name": "integrity", "Value": "sha256-aVY1Nq6bMgvQ71moBOwjdYBcuWJcjIAAt1g4+XIsTFE="}]}, {"Route": "ui-manager.zhln9tevi0.js", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\ui-manager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "35289"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"aVY1Nq6bMgvQ71moBOwjdYBcuWJcjIAAt1g4+XIsTFE=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:46:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zhln9tevi0"}, {"Name": "label", "Value": "ui-manager.js"}, {"Name": "integrity", "Value": "sha256-aVY1Nq6bMgvQ71moBOwjdYBcuWJcjIAAt1g4+XIsTFE="}]}, {"Route": "ui-manager.zhln9tevi0.js.gz", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\uvhoxifli3-zhln9tevi0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7057"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"S6Y2dIN39rkBvg2Af8QgOB9BTy/W1v1ebCYkn5vqejM=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 13:47:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zhln9tevi0"}, {"Name": "label", "Value": "ui-manager.js.gz"}, {"Name": "integrity", "Value": "sha256-S6Y2dIN39rkBvg2Af8QgOB9BTy/W1v1ebCYkn5vqejM="}]}]}