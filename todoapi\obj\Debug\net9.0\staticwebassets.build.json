{"Version": 1, "Hash": "Eadl2k0AKKq7Pro6a5hnLjY+IrLzV9lgRiBxhEiQemI=", "Source": "to<PERSON>ap<PERSON>", "BasePath": "_content/todoapi", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "todoapi\\wwwroot", "Source": "to<PERSON>ap<PERSON>", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "Pattern": "**"}], "Assets": [{"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\9vecr4ppa8-16oku2ib4b.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "index9#[.{fingerprint=16oku2ib4b}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "82ngmscpqw", "Integrity": "wJ6sk1NZvhl6Z3pn4pBrv6Wed46Rqy2TwWVpFpBS+k4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9.html", "FileLength": 18862, "LastWriteTime": "2025-09-28T12:35:28+00:00"}, {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\odg1uj9emm-5lbvunm6pn.gz", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/todoapi", "RelativePath": "index#[.{fingerprint=5lbvunm6pn}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cus234pdx0", "Integrity": "fL6DYJfZe8Kh5PgiL0pbcziLtCpR2DefJfiZd+wDREc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index.html", "FileLength": 765, "LastWriteTime": "2025-09-28T11:42:02+00:00"}, {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index.html", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5lbvunm6pn", "Integrity": "lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 1941, "LastWriteTime": "2025-09-28T03:04:20+00:00"}, {"Identity": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9.html", "SourceId": "to<PERSON>ap<PERSON>", "SourceType": "Discovered", "ContentRoot": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\", "BasePath": "_content/todoapi", "RelativePath": "index9#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "16oku2ib4b", "Integrity": "p0KvelnDSwL8iAS6MAwBF6Oxcmf79vfFndKrWXfqEuk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index9.html", "FileLength": 118816, "LastWriteTime": "2025-09-28T12:34:02+00:00"}], "Endpoints": [{"Route": "index.5lbvunm6pn.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\odg1uj9emm-5lbvunm6pn.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001305483029"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "765"}, {"Name": "ETag", "Value": "\"fL6DYJfZe8Kh5PgiL0pbcziLtCpR2DefJfiZd+wDREc=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 11:42:02 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5lbvunm6pn"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ="}]}, {"Route": "index.5lbvunm6pn.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1941"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 03:04:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5lbvunm6pn"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ="}]}, {"Route": "index.5lbvunm6pn.html.gz", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\odg1uj9emm-5lbvunm6pn.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "765"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"fL6DYJfZe8Kh5PgiL0pbcziLtCpR2DefJfiZd+wDREc=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 11:42:02 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5lbvunm6pn"}, {"Name": "label", "Value": "index.html.gz"}, {"Name": "integrity", "Value": "sha256-fL6DYJfZe8Kh5PgiL0pbcziLtCpR2DefJfiZd+wDREc="}]}, {"Route": "index.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\odg1uj9emm-5lbvunm6pn.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001305483029"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "765"}, {"Name": "ETag", "Value": "\"fL6DYJfZe8Kh5PgiL0pbcziLtCpR2DefJfiZd+wDREc=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 11:42:02 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ="}]}, {"Route": "index.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1941"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 03:04:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lzk+nYrQZsC0HAxR5SEE00+3rap2Cf12otjoih6qXdQ="}]}, {"Route": "index.html.gz", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\odg1uj9emm-5lbvunm6pn.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "765"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"fL6DYJfZe8Kh5PgiL0pbcziLtCpR2DefJfiZd+wDREc=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 11:42:02 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fL6DYJfZe8Kh5PgiL0pbcziLtCpR2DefJfiZd+wDREc="}]}, {"Route": "index9.16oku2ib4b.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\9vecr4ppa8-16oku2ib4b.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000053013837"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18862"}, {"Name": "ETag", "Value": "\"wJ6sk1NZvhl6Z3pn4pBrv6Wed46Rqy2TwWVpFpBS+k4=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 12:35:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"p0KvelnDSwL8iAS6MAwBF6Oxcmf79vfFndKrWXfqEuk=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "16oku2ib4b"}, {"Name": "label", "Value": "index9.html"}, {"Name": "integrity", "Value": "sha256-p0KvelnDSwL8iAS6MAwBF6Oxcmf79vfFndKrWXfqEuk="}]}, {"Route": "index9.16oku2ib4b.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "118816"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"p0KvelnDSwL8iAS6MAwBF6Oxcmf79vfFndKrWXfqEuk=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 12:34:02 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "16oku2ib4b"}, {"Name": "label", "Value": "index9.html"}, {"Name": "integrity", "Value": "sha256-p0KvelnDSwL8iAS6MAwBF6Oxcmf79vfFndKrWXfqEuk="}]}, {"Route": "index9.16oku2ib4b.html.gz", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\9vecr4ppa8-16oku2ib4b.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18862"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"wJ6sk1NZvhl6Z3pn4pBrv6Wed46Rqy2TwWVpFpBS+k4=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 12:35:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "16oku2ib4b"}, {"Name": "label", "Value": "index9.html.gz"}, {"Name": "integrity", "Value": "sha256-wJ6sk1NZvhl6Z3pn4pBrv6Wed46Rqy2TwWVpFpBS+k4="}]}, {"Route": "index9.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\9vecr4ppa8-16oku2ib4b.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000053013837"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18862"}, {"Name": "ETag", "Value": "\"wJ6sk1NZvhl6Z3pn4pBrv6Wed46Rqy2TwWVpFpBS+k4=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 12:35:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"p0KvelnDSwL8iAS6MAwBF6Oxcmf79vfFndKrWXfqEuk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p0KvelnDSwL8iAS6MAwBF6Oxcmf79vfFndKrWXfqEuk="}]}, {"Route": "index9.html", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\wwwroot\\index9.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "118816"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"p0KvelnDSwL8iAS6MAwBF6Oxcmf79vfFndKrWXfqEuk=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 12:34:02 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p0KvelnDSwL8iAS6MAwBF6Oxcmf79vfFndKrWXfqEuk="}]}, {"Route": "index9.html.gz", "AssetFile": "M:\\aDev25\\vanillaJS\\TodoAI25Solution\\todoapi\\obj\\Debug\\net9.0\\compressed\\9vecr4ppa8-16oku2ib4b.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18862"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"wJ6sk1NZvhl6Z3pn4pBrv6Wed46Rqy2TwWVpFpBS+k4=\""}, {"Name": "Last-Modified", "Value": "Sun, 28 Sep 2025 12:35:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wJ6sk1NZvhl6Z3pn4pBrv6Wed46Rqy2TwWVpFpBS+k4="}]}]}