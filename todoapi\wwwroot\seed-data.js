// Script to seed the database with sample data
const seedData = {
  "tasks": [
    {
      "id": "1758421279189",
      "title": "Haircut",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:21:19.189Z",
      "completedAt": null,
      "notes": "",
      "assignees": [
        {
          "id": "3",
          "name": "<PERSON><PERSON> John",
          "avatar": "#FF9800"
        },
        {
          "id": "4",
          "name": "<PERSON>",
          "avatar": "#9C27B0"
        }
      ]
    },
    {
      "id": "1758421299096",
      "title": "Find Job",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:21:39.096Z",
      "notes": "",
      "assignees": [
        {
          "id": "1",
          "name": "Dad",
          "avatar": "#4CAF50"
        },
        {
          "id": "2",
          "name": "Mom",
          "avatar": "#2196F3"
        },
        {
          "id": "3",
          "name": "Jr. John",
          "avatar": "#FF9800"
        },
        {
          "id": "5",
          "name": "Louie",
          "avatar": "#E91E63"
        }
      ]
    },
    {
      "id": "1758421330109",
      "title": "Clean the house",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:22:10.109Z",
      "notes": "",
      "assignees": [
        {
          "id": "1",
          "name": "Dad",
          "avatar": "#4CAF50"
        },
        {
          "id": "2",
          "name": "Mom",
          "avatar": "#2196F3"
        },
        {
          "id": "3",
          "name": "Jr. John",
          "avatar": "#FF9800"
        },
        {
          "id": "4",
          "name": "Mike",
          "avatar": "#9C27B0"
        }
      ]
    },
    {
      "id": "1758421344906",
      "title": "Catch the Rat",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:22:24.906Z",
      "notes": "",
      "assignees": [
        {
          "id": "5",
          "name": "Louie",
          "avatar": "#E91E63"
        }
      ]
    },
    {
      "id": "1758421583266",
      "title": "bof cart",
      "dueDate": "2025-09-22",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:26:23.266Z"
    },
    {
      "id": "1758421644469",
      "title": "HOA 99",
      "dueDate": "2025-10-01",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:27:24.469Z"
    },
    {
      "id": "1758421671769",
      "title": "HOUSE TAX",
      "dueDate": "2025-09-30",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:27:51.769Z"
    },
    {
      "id": "1758421704160",
      "title": "HOME DEPOT CREDIT",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:28:24.160Z"
    },
    {
      "id": "1758421759999",
      "title": "CHASE 1 CHASE 2",
      "dueDate": "2025-10-13",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:29:19.999Z"
    },
    {
      "id": "1758421781392",
      "title": "AMAZON STORE",
      "dueDate": "2025-10-13",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:29:41.392Z"
    },
    {
      "id": "1758421806737",
      "title": "CAR INSURANCE",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:30:06.737Z"
    },
    {
      "id": "1758421857545",
      "title": "EBT",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:30:57.545Z"
    },
    {
      "id": "1758421877929",
      "title": "E BIKE RETURN",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:31:17.929Z",
      "notes": "",
      "assignees": []
    },
    {
      "id": "17584********",
      "title": "YARD SALE",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:31:54.679Z"
    },
    {
      "id": "*************",
      "title": "PADIO COVER",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:32:44.122Z"
    },
    {
      "id": "*************",
      "title": "NEW 2  CHASE ACCOUNT",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:33:50.020Z"
    },
    {
      "id": "*************",
      "title": "GET UP 6 AM",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:36:09.054Z"
    },
    {
      "id": "*************",
      "title": "INTERNET CLOSE 8 PM",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:36:40.215Z",
      "notes": "",
      "assignees": []
    },
    {
      "id": "*************",
      "title": "FALL CLEANING",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:37:46.906Z"
    },
    {
      "id": "*************",
      "title": "SINK FIX",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:38:01.498Z"
    },
    {
      "id": "1758422308786",
      "title": "CAR TIRE PRESSURE CHECK",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:38:28.786Z"
    },
    {
      "id": "1758422331911",
      "title": "E BIKES MAINTANCE",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:38:51.911Z"
    },
    {
      "id": "1758422360850",
      "title": "MAKE TENT AVALIABLE",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:39:20.850Z"
    },
    {
      "id": "1758422378300",
      "title": "WATER TREES",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:39:38.300Z"
    },
    {
      "id": "1758422393421",
      "title": "CLEAN DISHES",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:39:53.421Z"
    },
    {
      "id": "1758422408187",
      "title": "CLEAN GARAGE",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:40:08.187Z"
    },
    {
      "id": "1758422441823",
      "title": "CLEAN JUNK MAIL",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:40:41.823Z"
    },
    {
      "id": "1758422510365",
      "title": "FANS",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:41:50.365Z"
    },
    {
      "id": "1758423452341",
      "title": "TAX AJUSTMENT",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T02:57:32.341Z"
    },
    {
      "id": "1758428920512",
      "title": "Apple Card",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-21T04:28:40.512Z"
    },
    {
      "id": "1758765963846",
      "title": "HOUSE TAX (Copy)",
      "dueDate": "2025-09-30",
      "isImportant": false,
      "assignees": [],
      "listId": null,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-25T02:06:03.846Z"
    },
    {
      "id": "1758766063376",
      "title": "Pay Apple card",
      "listId": "list-*************",
      "isImportant": true,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-25T02:07:43.377Z"
    },
    {
      "id": "1758766071532",
      "title": "Pay Apple card (Copy)",
      "isImportant": false,
      "assignees": [],
      "listId": "list-*************",
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-25T02:07:51.532Z"
    },
    {
      "id": "1758768295506",
      "title": "ger chiken uncook",
      "listId": "list-*************",
      "isImportant": false,
      "myDayDate": null,
      "isCompleted": false,
      "createdAt": "2025-09-25T02:44:55.506Z"
    }
  ],
  "lists": [
    {
      "id": "list-*************",
      "name": "Home",
      "icon": "📁",
      "smart": false
    },
    {
      "id": "list-*************",
      "name": "payment",
      "icon": "📁",
      "smart": false
    },
    {
      "id": "list-*************",
      "name": "DISCIPLINE",
      "icon": "📁",
      "smart": false
    }
  ]
};

// Extract unique users from task assignees
const users = new Map();
seedData.tasks.forEach(task => {
  if (task.assignees) {
    task.assignees.forEach(assignee => {
      if (!users.has(assignee.id)) {
        users.set(assignee.id, {
          id: assignee.id,
          name: assignee.name,
          avatar: assignee.avatar,
          email: `${assignee.name.toLowerCase().replace(/\s+/g, '.')}@example.com`
        });
      }
    });
  }
});

seedData.users = Array.from(users.values());

console.log('Seed data prepared:', {
  tasks: seedData.tasks.length,
  lists: seedData.lists.length,
  users: seedData.users.length
});
