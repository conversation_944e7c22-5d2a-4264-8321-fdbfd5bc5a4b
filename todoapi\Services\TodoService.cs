using Microsoft.EntityFrameworkCore;
using todoapi.Data;
using todoapi.DTOs;
using todoapi.Models;

namespace todoapi.Services;

public interface ITodoService
{
    // Tasks
    Task<IEnumerable<TodoTaskDto>> GetAllTasksAsync();
    Task<TodoTaskDto?> GetTaskByIdAsync(string id);
    Task<TodoTaskDto> CreateTaskAsync(CreateTodoTaskDto taskDto);
    Task<TodoTaskDto?> UpdateTaskAsync(string id, UpdateTodoTaskDto taskDto);
    Task<bool> DeleteTaskAsync(string id);
    Task<bool> BulkOperationAsync(BulkOperationDto bulkDto);
    Task<IEnumerable<TodoTaskDto>> GetTasksByListAsync(string listId);
    Task<IEnumerable<TodoTaskDto>> GetTasksByUserAsync(string userId);
    Task<IEnumerable<TodoTaskDto>> GetImportantTasksAsync();
    Task<IEnumerable<TodoTaskDto>> GetMyDayTasksAsync(string date);
    Task<IEnumerable<TodoTaskDto>> GetPlannedTasksAsync();

    // Lists
    Task<IEnumerable<TodoListDto>> GetAllListsAsync();
    Task<TodoListDto?> GetListByIdAsync(string id);
    Task<TodoListDto> CreateListAsync(CreateTodoListDto listDto);
    Task<TodoListDto?> UpdateListAsync(string id, CreateTodoListDto listDto);
    Task<bool> DeleteListAsync(string id);

    // Users
    Task<IEnumerable<UserDto>> GetAllUsersAsync();
    Task<UserDto?> GetUserByIdAsync(string id);
    Task<UserDto> CreateUserAsync(CreateUserDto userDto);
    Task<UserDto?> UpdateUserAsync(string id, CreateUserDto userDto);
    Task<bool> DeleteUserAsync(string id);

    // Statistics
    Task<object> GetStatsAsync();
}

public class TodoService : ITodoService
{
    private readonly TodoDbContext _context;

    public TodoService(TodoDbContext context)
    {
        _context = context;
    }

    #region Tasks

    public async Task<IEnumerable<TodoTaskDto>> GetAllTasksAsync()
    {
        var tasks = await _context.Tasks
            .Include(t => t.Assignees)
            .ThenInclude(ta => ta.User)
            .ToListAsync();

        return tasks.Select(MapToTaskDto);
    }

    public async Task<TodoTaskDto?> GetTaskByIdAsync(string id)
    {
        var task = await _context.Tasks
            .Include(t => t.Assignees)
            .ThenInclude(ta => ta.User)
            .FirstOrDefaultAsync(t => t.Id == id);

        return task == null ? null : MapToTaskDto(task);
    }

    public async Task<TodoTaskDto> CreateTaskAsync(CreateTodoTaskDto taskDto)
    {
        var task = new TodoTask
        {
            Id = Guid.NewGuid().ToString(),
            Title = taskDto.Title,
            Notes = taskDto.Notes,
            DueDate = taskDto.DueDate,
            Recurrence = taskDto.Recurrence,
            IsImportant = taskDto.IsImportant,
            MyDayDate = taskDto.MyDayDate,
            ListId = taskDto.ListId,
            CreatedAt = DateTime.UtcNow
        };

        _context.Tasks.Add(task);

        // Add assignees
        foreach (var assigneeId in taskDto.AssigneeIds)
        {
            _context.TaskAssignees.Add(new TaskAssignee
            {
                TaskId = task.Id,
                UserId = assigneeId,
                AssignedAt = DateTime.UtcNow
            });
        }

        await _context.SaveChangesAsync();

        // Reload with includes
        var createdTask = await _context.Tasks
            .Include(t => t.Assignees)
            .ThenInclude(ta => ta.User)
            .FirstAsync(t => t.Id == task.Id);

        return MapToTaskDto(createdTask);
    }

    public async Task<TodoTaskDto?> UpdateTaskAsync(string id, UpdateTodoTaskDto taskDto)
    {
        var task = await _context.Tasks
            .Include(t => t.Assignees)
            .FirstOrDefaultAsync(t => t.Id == id);

        if (task == null) return null;

        // Update properties if provided
        if (taskDto.Title != null) task.Title = taskDto.Title;
        if (taskDto.Notes != null) task.Notes = taskDto.Notes;
        if (taskDto.IsCompleted.HasValue)
        {
            task.IsCompleted = taskDto.IsCompleted.Value;
            task.CompletedAt = task.IsCompleted ? DateTime.UtcNow : null;
        }
        if (taskDto.IsImportant.HasValue) task.IsImportant = taskDto.IsImportant.Value;
        if (taskDto.DueDate.HasValue) task.DueDate = taskDto.DueDate;
        if (taskDto.Recurrence != null) task.Recurrence = taskDto.Recurrence;
        if (taskDto.MyDayDate != null) task.MyDayDate = taskDto.MyDayDate;
        if (taskDto.ListId != null) task.ListId = taskDto.ListId;

        // Update assignees if provided
        if (taskDto.AssigneeIds != null)
        {
            // Remove existing assignees
            var existingAssignees = task.Assignees.ToList();
            _context.TaskAssignees.RemoveRange(existingAssignees);

            // Add new assignees
            foreach (var assigneeId in taskDto.AssigneeIds)
            {
                _context.TaskAssignees.Add(new TaskAssignee
                {
                    TaskId = task.Id,
                    UserId = assigneeId,
                    AssignedAt = DateTime.UtcNow
                });
            }
        }

        await _context.SaveChangesAsync();

        // Reload with includes
        var updatedTask = await _context.Tasks
            .Include(t => t.Assignees)
            .ThenInclude(ta => ta.User)
            .FirstAsync(t => t.Id == id);

        return MapToTaskDto(updatedTask);
    }

    public async Task<bool> DeleteTaskAsync(string id)
    {
        var task = await _context.Tasks.FindAsync(id);
        if (task == null) return false;

        _context.Tasks.Remove(task);
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> BulkOperationAsync(BulkOperationDto bulkDto)
    {
        var tasks = await _context.Tasks
            .Where(t => bulkDto.TaskIds.Contains(t.Id))
            .ToListAsync();

        if (!tasks.Any()) return false;

        switch (bulkDto.Operation.ToLower())
        {
            case "complete":
                foreach (var task in tasks)
                {
                    task.IsCompleted = true;
                    task.CompletedAt = DateTime.UtcNow;
                }
                break;
            case "delete":
                _context.Tasks.RemoveRange(tasks);
                break;
            case "important":
                foreach (var task in tasks)
                {
                    task.IsImportant = !task.IsImportant;
                }
                break;
            default:
                return false;
        }

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<IEnumerable<TodoTaskDto>> GetTasksByListAsync(string listId)
    {
        var tasks = await _context.Tasks
            .Include(t => t.Assignees)
            .ThenInclude(ta => ta.User)
            .Where(t => t.ListId == listId)
            .ToListAsync();

        return tasks.Select(MapToTaskDto);
    }

    public async Task<IEnumerable<TodoTaskDto>> GetTasksByUserAsync(string userId)
    {
        var tasks = await _context.Tasks
            .Include(t => t.Assignees)
            .ThenInclude(ta => ta.User)
            .Where(t => t.Assignees.Any(a => a.UserId == userId))
            .ToListAsync();

        return tasks.Select(MapToTaskDto);
    }

    public async Task<IEnumerable<TodoTaskDto>> GetImportantTasksAsync()
    {
        var tasks = await _context.Tasks
            .Include(t => t.Assignees)
            .ThenInclude(ta => ta.User)
            .Where(t => t.IsImportant)
            .ToListAsync();

        return tasks.Select(MapToTaskDto);
    }

    public async Task<IEnumerable<TodoTaskDto>> GetMyDayTasksAsync(string date)
    {
        var tasks = await _context.Tasks
            .Include(t => t.Assignees)
            .ThenInclude(ta => ta.User)
            .Where(t => t.MyDayDate == date)
            .ToListAsync();

        return tasks.Select(MapToTaskDto);
    }

    public async Task<IEnumerable<TodoTaskDto>> GetPlannedTasksAsync()
    {
        var tasks = await _context.Tasks
            .Include(t => t.Assignees)
            .ThenInclude(ta => ta.User)
            .Where(t => t.DueDate != null)
            .ToListAsync();

        return tasks.Select(MapToTaskDto);
    }

    #endregion

    #region Lists

    public async Task<IEnumerable<TodoListDto>> GetAllListsAsync()
    {
        var lists = await _context.Lists
            .Include(l => l.Tasks)
            .ToListAsync();

        return lists.Select(MapToListDto);
    }

    public async Task<TodoListDto?> GetListByIdAsync(string id)
    {
        var list = await _context.Lists
            .Include(l => l.Tasks)
            .FirstOrDefaultAsync(l => l.Id == id);

        return list == null ? null : MapToListDto(list);
    }

    public async Task<TodoListDto> CreateListAsync(CreateTodoListDto listDto)
    {
        var list = new TodoList
        {
            Id = Guid.NewGuid().ToString(),
            Name = listDto.Name,
            Icon = listDto.Icon,
            Smart = false,
            CreatedAt = DateTime.UtcNow
        };

        _context.Lists.Add(list);
        await _context.SaveChangesAsync();

        return MapToListDto(list);
    }

    public async Task<TodoListDto?> UpdateListAsync(string id, CreateTodoListDto listDto)
    {
        var list = await _context.Lists.FindAsync(id);
        if (list == null) return null;

        list.Name = listDto.Name;
        if (listDto.Icon != null) list.Icon = listDto.Icon;

        await _context.SaveChangesAsync();

        return MapToListDto(list);
    }

    public async Task<bool> DeleteListAsync(string id)
    {
        var list = await _context.Lists.FindAsync(id);
        if (list == null) return false;

        // Update tasks to remove list reference
        var tasks = await _context.Tasks.Where(t => t.ListId == id).ToListAsync();
        foreach (var task in tasks)
        {
            task.ListId = null;
        }

        _context.Lists.Remove(list);
        await _context.SaveChangesAsync();
        return true;
    }

    #endregion

    #region Users

    public async Task<IEnumerable<UserDto>> GetAllUsersAsync()
    {
        var users = await _context.Users
            .Include(u => u.TaskAssignments)
            .ThenInclude(ta => ta.Task)
            .ToListAsync();

        return users.Select(MapToUserDto);
    }

    public async Task<UserDto?> GetUserByIdAsync(string id)
    {
        var user = await _context.Users
            .Include(u => u.TaskAssignments)
            .ThenInclude(ta => ta.Task)
            .FirstOrDefaultAsync(u => u.Id == id);

        return user == null ? null : MapToUserDto(user);
    }

    public async Task<UserDto> CreateUserAsync(CreateUserDto userDto)
    {
        var user = new User
        {
            Id = Guid.NewGuid().ToString(),
            Name = userDto.Name,
            Email = userDto.Email,
            Avatar = userDto.Avatar,
            CreatedAt = DateTime.UtcNow
        };

        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        return MapToUserDto(user);
    }

    public async Task<UserDto?> UpdateUserAsync(string id, CreateUserDto userDto)
    {
        var user = await _context.Users.FindAsync(id);
        if (user == null) return null;

        user.Name = userDto.Name;
        user.Email = userDto.Email;
        user.Avatar = userDto.Avatar;

        await _context.SaveChangesAsync();

        return MapToUserDto(user);
    }

    public async Task<bool> DeleteUserAsync(string id)
    {
        var user = await _context.Users.FindAsync(id);
        if (user == null) return false;

        _context.Users.Remove(user);
        await _context.SaveChangesAsync();
        return true;
    }

    #endregion

    #region Statistics

    public async Task<object> GetStatsAsync()
    {
        var today = DateTime.UtcNow.Date;
        var todayString = today.ToString("yyyy-MM-dd");

        var totalTasks = await _context.Tasks.CountAsync();
        var completedTasks = await _context.Tasks.CountAsync(t => t.IsCompleted);
        var importantTasks = await _context.Tasks.CountAsync(t => t.IsImportant && !t.IsCompleted);
        var overdueTasks = await _context.Tasks.CountAsync(t => t.DueDate < today && !t.IsCompleted);
        var todayCompleted = await _context.Tasks.CountAsync(t => 
            t.CompletedAt != null && t.CompletedAt.Value.Date == today);

        return new
        {
            total = totalTasks,
            completed = completedTasks,
            important = importantTasks,
            overdue = overdueTasks,
            completedToday = todayCompleted
        };
    }

    #endregion

    #region Mapping Methods

    private static TodoTaskDto MapToTaskDto(TodoTask task)
    {
        return new TodoTaskDto
        {
            Id = task.Id,
            Title = task.Title,
            Notes = task.Notes,
            IsCompleted = task.IsCompleted,
            IsImportant = task.IsImportant,
            CreatedAt = task.CreatedAt,
            CompletedAt = task.CompletedAt,
            DueDate = task.DueDate,
            Recurrence = task.Recurrence,
            MyDayDate = task.MyDayDate,
            ListId = task.ListId,
            Assignees = task.Assignees?.Select(ta => new AssigneeDto
            {
                Id = ta.User.Id,
                Name = ta.User.Name,
                Avatar = ta.User.Avatar
            }).ToList() ?? new List<AssigneeDto>()
        };
    }

    private static TodoListDto MapToListDto(TodoList list)
    {
        return new TodoListDto
        {
            Id = list.Id,
            Name = list.Name,
            Icon = list.Icon,
            Smart = list.Smart,
            CreatedAt = list.CreatedAt,
            TaskCount = list.Tasks?.Count(t => !t.IsCompleted) ?? 0
        };
    }

    private static UserDto MapToUserDto(User user)
    {
        return new UserDto
        {
            Id = user.Id,
            Name = user.Name,
            Email = user.Email,
            Avatar = user.Avatar,
            CreatedAt = user.CreatedAt,
            TaskCount = user.TaskAssignments?.Count(ta => !ta.Task.IsCompleted) ?? 0
        };
    }

    #endregion
}