using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using todoapi.Controllers;
using todoapi.DTOs;
using todoapi.Services;

namespace TestProject;

public class UsersControllerTests
{
    private readonly Mock<ITodoService> _mockTodoService;
    private readonly Mock<ILogger<UsersController>> _mockLogger;
    private readonly UsersController _controller;

    public UsersControllerTests()
    {
        _mockTodoService = new Mock<ITodoService>();
        _mockLogger = new Mock<ILogger<UsersController>>();
        _controller = new UsersController(_mockTodoService.Object, _mockLogger.Object);
    }

    #region GetAllUsers Tests

    [Fact]
    public async Task GetAllUsers_ReturnsOkResult_WithUsers()
    {
        // Arrange
        var users = new List<UserDto>
        {
            new UserDto { Id = "1", Name = "<PERSON>", Email = "<EMAIL>", Avatar = "#FF5722", TaskCount = 5 },
            new UserDto { Id = "2", Name = "<PERSON>", Email = "<EMAIL>", Avatar = "#2196F3", TaskCount = 3 }
        };
        _mockTodoService.Setup(s => s.GetAllUsersAsync()).ReturnsAsync(users);

        // Act
        var result = await _controller.GetAllUsers();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<ApiResponse<IEnumerable<UserDto>>>(okResult.Value);
        Assert.True(response.Success);
        Assert.Equal("Users retrieved successfully", response.Message);
        Assert.Equal(2, response.Data!.Count());
        _mockTodoService.Verify(s => s.GetAllUsersAsync(), Times.Once);
    }

    [Fact]
    public async Task GetAllUsers_ReturnsEmptyList_WhenNoUsers()
    {
        // Arrange
        var users = new List<UserDto>();
        _mockTodoService.Setup(s => s.GetAllUsersAsync()).ReturnsAsync(users);

        // Act
        var result = await _controller.GetAllUsers();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<ApiResponse<IEnumerable<UserDto>>>(okResult.Value);
        Assert.True(response.Success);
        Assert.Empty(response.Data!);
    }

    [Fact]
    public async Task GetAllUsers_ReturnsInternalServerError_WhenExceptionThrown()
    {
        // Arrange
        _mockTodoService.Setup(s => s.GetAllUsersAsync()).ThrowsAsync(new Exception("Database error"));

        // Act
        var result = await _controller.GetAllUsers();

        // Assert
        var statusCodeResult = Assert.IsType<ObjectResult>(result.Result);
        Assert.Equal(500, statusCodeResult.StatusCode);
        var response = Assert.IsType<ApiResponse<IEnumerable<UserDto>>>(statusCodeResult.Value);
        Assert.False(response.Success);
        Assert.Equal("Internal server error", response.Message);
        Assert.Contains("Database error", response.Errors);
    }

    #endregion

    #region GetUser Tests

    [Fact]
    public async Task GetUser_ReturnsOkResult_WhenUserExists()
    {
        // Arrange
        var userId = "1";
        var user = new UserDto { Id = userId, Name = "John Doe", Email = "<EMAIL>", Avatar = "#FF5722", TaskCount = 5 };
        _mockTodoService.Setup(s => s.GetUserByIdAsync(userId)).ReturnsAsync(user);

        // Act
        var result = await _controller.GetUser(userId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<ApiResponse<UserDto>>(okResult.Value);
        Assert.True(response.Success);
        Assert.Equal("User retrieved successfully", response.Message);
        Assert.Equal(userId, response.Data!.Id);
        Assert.Equal("John Doe", response.Data.Name);
    }

    [Fact]
    public async Task GetUser_ReturnsNotFound_WhenUserDoesNotExist()
    {
        // Arrange
        var userId = "nonexistent";
        _mockTodoService.Setup(s => s.GetUserByIdAsync(userId)).ReturnsAsync((UserDto?)null);

        // Act
        var result = await _controller.GetUser(userId);

        // Assert
        var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
        var response = Assert.IsType<ApiResponse<UserDto>>(notFoundResult.Value);
        Assert.False(response.Success);
        Assert.Equal("User not found", response.Message);
    }

    [Fact]
    public async Task GetUser_ReturnsInternalServerError_WhenExceptionThrown()
    {
        // Arrange
        var userId = "1";
        _mockTodoService.Setup(s => s.GetUserByIdAsync(userId)).ThrowsAsync(new Exception("Database error"));

        // Act
        var result = await _controller.GetUser(userId);

        // Assert
        var statusCodeResult = Assert.IsType<ObjectResult>(result.Result);
        Assert.Equal(500, statusCodeResult.StatusCode);
        var response = Assert.IsType<ApiResponse<UserDto>>(statusCodeResult.Value);
        Assert.False(response.Success);
        Assert.Equal("Internal server error", response.Message);
    }

    #endregion

    #region CreateUser Tests

    [Fact]
    public async Task CreateUser_ReturnsCreatedResult_WithValidUser()
    {
        // Arrange
        var createUserDto = new CreateUserDto { Name = "John Doe", Email = "<EMAIL>", Avatar = "#FF5722" };
        var createdUser = new UserDto { Id = "1", Name = "John Doe", Email = "<EMAIL>", Avatar = "#FF5722", TaskCount = 0 };
        _mockTodoService.Setup(s => s.CreateUserAsync(createUserDto)).ReturnsAsync(createdUser);

        // Act
        var result = await _controller.CreateUser(createUserDto);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        Assert.Equal(nameof(_controller.GetUser), createdResult.ActionName);
        Assert.Equal("1", createdResult.RouteValues!["id"]);
        var response = Assert.IsType<ApiResponse<UserDto>>(createdResult.Value);
        Assert.True(response.Success);
        Assert.Equal("User created successfully", response.Message);
        Assert.Equal("John Doe", response.Data!.Name);
    }

    [Fact]
    public async Task CreateUser_ReturnsBadRequest_WhenModelStateInvalid()
    {
        // Arrange
        var createUserDto = new CreateUserDto { Name = "", Email = "invalid-email", Avatar = "#FF5722" };
        _controller.ModelState.AddModelError("Name", "Name is required");
        _controller.ModelState.AddModelError("Email", "Invalid email format");

        // Act
        var result = await _controller.CreateUser(createUserDto);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
        var response = Assert.IsType<ApiResponse<UserDto>>(badRequestResult.Value);
        Assert.False(response.Success);
        Assert.Equal("Validation failed", response.Message);
        Assert.Contains("Name is required", response.Errors);
        Assert.Contains("Invalid email format", response.Errors);
    }

    [Fact]
    public async Task CreateUser_ReturnsInternalServerError_WhenExceptionThrown()
    {
        // Arrange
        var createUserDto = new CreateUserDto { Name = "John Doe", Email = "<EMAIL>", Avatar = "#FF5722" };
        _mockTodoService.Setup(s => s.CreateUserAsync(createUserDto)).ThrowsAsync(new Exception("Database error"));

        // Act
        var result = await _controller.CreateUser(createUserDto);

        // Assert
        var statusCodeResult = Assert.IsType<ObjectResult>(result.Result);
        Assert.Equal(500, statusCodeResult.StatusCode);
        var response = Assert.IsType<ApiResponse<UserDto>>(statusCodeResult.Value);
        Assert.False(response.Success);
        Assert.Equal("Internal server error", response.Message);
    }

    #endregion

    #region UpdateUser Tests

    [Fact]
    public async Task UpdateUser_ReturnsOkResult_WhenUserUpdatedSuccessfully()
    {
        // Arrange
        var userId = "1";
        var updateUserDto = new CreateUserDto { Name = "John Updated", Email = "<EMAIL>", Avatar = "#2196F3" };
        var updatedUser = new UserDto { Id = userId, Name = "John Updated", Email = "<EMAIL>", Avatar = "#2196F3", TaskCount = 5 };
        _mockTodoService.Setup(s => s.UpdateUserAsync(userId, updateUserDto)).ReturnsAsync(updatedUser);

        // Act
        var result = await _controller.UpdateUser(userId, updateUserDto);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<ApiResponse<UserDto>>(okResult.Value);
        Assert.True(response.Success);
        Assert.Equal("User updated successfully", response.Message);
        Assert.Equal("John Updated", response.Data!.Name);
    }

    [Fact]
    public async Task UpdateUser_ReturnsNotFound_WhenUserDoesNotExist()
    {
        // Arrange
        var userId = "nonexistent";
        var updateUserDto = new CreateUserDto { Name = "John Updated", Email = "<EMAIL>", Avatar = "#2196F3" };
        _mockTodoService.Setup(s => s.UpdateUserAsync(userId, updateUserDto)).ReturnsAsync((UserDto?)null);

        // Act
        var result = await _controller.UpdateUser(userId, updateUserDto);

        // Assert
        var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
        var response = Assert.IsType<ApiResponse<UserDto>>(notFoundResult.Value);
        Assert.False(response.Success);
        Assert.Equal("User not found", response.Message);
    }

    [Fact]
    public async Task UpdateUser_ReturnsInternalServerError_WhenExceptionThrown()
    {
        // Arrange
        var userId = "1";
        var updateUserDto = new CreateUserDto { Name = "John Updated", Email = "<EMAIL>", Avatar = "#2196F3" };
        _mockTodoService.Setup(s => s.UpdateUserAsync(userId, updateUserDto)).ThrowsAsync(new Exception("Database error"));

        // Act
        var result = await _controller.UpdateUser(userId, updateUserDto);

        // Assert
        var statusCodeResult = Assert.IsType<ObjectResult>(result.Result);
        Assert.Equal(500, statusCodeResult.StatusCode);
        var response = Assert.IsType<ApiResponse<UserDto>>(statusCodeResult.Value);
        Assert.False(response.Success);
        Assert.Equal("Internal server error", response.Message);
    }

    #endregion

    #region DeleteUser Tests

    [Fact]
    public async Task DeleteUser_ReturnsOkResult_WhenUserDeletedSuccessfully()
    {
        // Arrange
        var userId = "1";
        _mockTodoService.Setup(s => s.DeleteUserAsync(userId)).ReturnsAsync(true);

        // Act
        var result = await _controller.DeleteUser(userId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<ApiResponse<bool>>(okResult.Value);
        Assert.True(response.Success);
        Assert.Equal("User deleted successfully", response.Message);
        Assert.True(response.Data);
        _mockTodoService.Verify(s => s.DeleteUserAsync(userId), Times.Once);
    }

    [Fact]
    public async Task DeleteUser_ReturnsNotFound_WhenUserDoesNotExist()
    {
        // Arrange
        var userId = "nonexistent";
        _mockTodoService.Setup(s => s.DeleteUserAsync(userId)).ReturnsAsync(false);

        // Act
        var result = await _controller.DeleteUser(userId);

        // Assert
        var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
        var response = Assert.IsType<ApiResponse<bool>>(notFoundResult.Value);
        Assert.False(response.Success);
        Assert.Equal("User not found", response.Message);
    }

    [Fact]
    public async Task DeleteUser_ReturnsInternalServerError_WhenExceptionThrown()
    {
        // Arrange
        var userId = "1";
        _mockTodoService.Setup(s => s.DeleteUserAsync(userId)).ThrowsAsync(new Exception("Database error"));

        // Act
        var result = await _controller.DeleteUser(userId);

        // Assert
        var statusCodeResult = Assert.IsType<ObjectResult>(result.Result);
        Assert.Equal(500, statusCodeResult.StatusCode);
        var response = Assert.IsType<ApiResponse<bool>>(statusCodeResult.Value);
        Assert.False(response.Success);
        Assert.Equal("Internal server error", response.Message);
        Assert.Contains("Database error", response.Errors);
    }

    #endregion

    #region Constructor Tests

    [Fact]
    public void Constructor_ThrowsArgumentNullException_WhenTodoServiceIsNull()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => new UsersController(null!, _mockLogger.Object));
    }

    [Fact]
    public void Constructor_ThrowsArgumentNullException_WhenLoggerIsNull()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => new UsersController(_mockTodoService.Object, null!));
    }

    #endregion
}
